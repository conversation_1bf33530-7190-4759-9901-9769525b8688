<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أمر الإنتاج المحدث</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-header {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn {
            background: #0078d4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #106ebe;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-info {
            background: #17a2b8;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }

        .horizontal-modal {
            max-width: 98vw;
            width: 1600px;
            max-height: 95vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header.compact {
            padding: 12px 20px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-body.horizontal {
            padding: 15px 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-footer.compact {
            padding: 12px 20px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 15px;
        }

        .form-group.compact {
            margin-bottom: 8px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-label.compact {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #495057;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-input.compact, .form-select.compact, .form-textarea.compact {
            padding: 6px 8px;
            font-size: 12px;
            height: 32px;
            border: 1px solid #ced4da;
        }

        .form-textarea.compact {
            height: auto;
            min-height: 50px;
            resize: vertical;
        }

        .btn.compact {
            padding: 6px 12px;
            font-size: 12px;
            height: 32px;
        }

        .btn-sm.compact {
            padding: 4px 8px;
            font-size: 11px;
            height: 28px;
        }

        /* السطر الأول (فوق): المعلومات الأساسية */
        .order-header-row {
            display: flex;
            gap: 8px;
            align-items: end;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        /* السطر الثاني (تحت): جدول الأصناف */
        .order-items-row {
            margin-top: 0;
            margin-bottom: 10px;
        }

        /* Table Styles */
        .items-table-container {
            max-height: 250px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
            background: white;
        }

        /* جدول الأصناف بدون تمرير */
        .items-table-container-full {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
            background: white;
            /* بدون max-height أو overflow */
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .items-table-full {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .order-items-section {
            margin: 5px 0;
        }

        /* رأس الجدول المضغوط */
        .items-header-compact {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            height: 32px; /* نفس ارتفاع السطر الأول */
        }

        .section-title-compact {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        /* جدول الأصناف المضغوط */
        .items-table-container-compact {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            max-height: 200px; /* ارتفاع محدود */
            overflow-y: auto;
        }

        .items-table-compact {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .items-table-compact th {
            background: #f8f9fa;
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            text-align: center;
            font-size: 10px;
            font-weight: bold;
            height: 28px;
        }

        .items-table-compact td {
            padding: 4px;
            border: 1px solid #dee2e6;
            text-align: center;
            height: 32px; /* نفس ارتفاع السطر الأول */
        }

        .items-table th {
            background: #f8f9fa;
            padding: 8px 6px;
            border: 1px solid #dee2e6;
            font-weight: 600;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .items-table td {
            padding: 6px 4px;
            border: 1px solid #dee2e6;
            text-align: center;
            vertical-align: middle;
        }

        .items-table input, .items-table select {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            font-size: 11px;
            height: 28px;
        }

        .section-title {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #495057;
            font-weight: 600;
        }

        .order-header-section, .order-footer-section {
            margin-bottom: 15px;
        }

        .order-items-section {
            margin: 15px 0;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .horizontal-modal {
                width: 95vw;
            }
            
            .form-row-6 {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-row-6 {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .form-row-3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> اختبار أمر الإنتاج المحدث</h1>
            <p>تصميم أفقي مضغوط مع حساب تلقائي للمساحة والتكلفة</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> آلية العمل المحدثة</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h4 style="color: #0078d4; margin-bottom: 10px;">🏗️ أصناف العميل (لا تُخصم من المخزون):</h4>
                <p style="margin: 5px 0;">• الأبواب، النوافذ، الجدران، الأسقف التي يريد العميل دهانها</p>
                <p style="margin: 5px 0;">• هذه الأصناف ملك العميل ولا تُحسب من مخزون الشركة</p>

                <h4 style="color: #28a745; margin: 15px 0 10px 0;">🎨 المواد الخام (تُخصم من المخزون):</h4>
                <p style="margin: 5px 0;">• الدهانات والمواد المساعدة المستخدمة في العمل</p>
                <p style="margin: 5px 0;">• يتم خصمها من المخزون عند إصدار الفاتورة</p>
                <p style="margin: 5px 0;">• معدل الاستهلاك: 1 لتر دهان لكل 8 متر مربع</p>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-play"></i> اختبار النافذة الجديدة</h3>
            <p>انقر على الزر أدناه لفتح نافذة أمر الإنتاج بالتصميم الجديد:</p>
            <button class="btn" onclick="openTestModal()">
                <i class="fas fa-cogs"></i> فتح أمر إنتاج جديد
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-list"></i> الميزات الجديدة</h3>
            <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>سطرين فوق بعض:</strong> السطر الأول فوق، السطر الثاني تحت مباشرة
                </li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>عرض مضغوط:</strong> جميع المربعات بعرض 120px متساوي
                </li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>جدول مضغوط:</strong> جدول الأصناف بنفس ارتفاع السطر الأول
                </li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>حساب تلقائي:</strong> الطول × العرض × العدد = المساحة الإجمالية
                </li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>إجماليات تلقائية:</strong> حساب إجمالي المساحة والتكلفة تلقائياً
                </li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>طباعة شاملة:</strong> وصف الصنف، الأبعاد، نوع ولون الدهان، العدد
                </li>
                <li style="padding: 8px 0;">
                    <i class="fas fa-check" style="color: #28a745;"></i> <strong>تطبيق شامل:</strong> نفس التحسينات على كامل البرنامج
                </li>
            </ul>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script>
        // بيانات تجريبية
        const customers = [
            { id: '1', name: 'شركة الأحمد للمقاولات' },
            { id: '2', name: 'مؤسسة النور التجارية' },
            { id: '3', name: 'شركة البناء الحديث' }
        ];

        const paintTypes = [
            { id: '1', name: 'دهان أكريليك', color: 'أبيض', pricePerMeter: 25, costPerMeter: 15 },
            { id: '2', name: 'دهان زيتي', color: 'أزرق', pricePerMeter: 35, costPerMeter: 20 },
            { id: '3', name: 'دهان بلاستيك', color: 'أخضر', pricePerMeter: 20, costPerMeter: 12 },
            { id: '4', name: 'دهان معدني', color: 'رمادي', pricePerMeter: 45, costPerMeter: 25 }
        ];

        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function generateOrderNumber() {
            const date = new Date();
            const year = date.getFullYear().toString().substr(-2);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const orderCount = Math.floor(Math.random() * 999) + 1;
            return `ORD-${year}${month}${day}-${orderCount.toString().padStart(3, '0')}`;
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function openTestModal() {
            const modalContent = `
                <div class="modal-overlay" onclick="closeModal()">
                    <div class="modal-content horizontal-modal" onclick="event.stopPropagation()">
                        <div class="modal-header compact">
                            <h3>أمر إنتاج جديد - التصميم المحدث</h3>
                            <button class="modal-close" onclick="closeModal()">&times;</button>
                        </div>
                        <form onsubmit="saveTestOrder(event)">
                            <div class="modal-body horizontal">
                                <!-- السطر الأول (فوق): المعلومات الأساسية -->
                                <div class="order-header-row">
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">رقم الأمر</label>
                                        <input type="text" class="form-input compact" name="orderNumber" value="${generateOrderNumber()}" readonly>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">العميل *</label>
                                        <select class="form-select compact" name="customerId" required>
                                            <option value="">اختر العميل</option>
                                            ${customers.map(customer => `
                                                <option value="${customer.id}">
                                                    ${customer.name}
                                                </option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">تاريخ الأمر</label>
                                        <input type="date" class="form-input compact" name="orderDate" value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">تاريخ التسليم</label>
                                        <input type="date" class="form-input compact" name="deliveryDate" required>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">الحالة</label>
                                        <select class="form-select compact" name="status">
                                            <option value="pending">في الانتظار</option>
                                            <option value="in-progress">قيد التنفيذ</option>
                                            <option value="completed">مكتمل</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">الأولوية</label>
                                        <select class="form-select compact" name="priority">
                                            <option value="normal">عادية</option>
                                            <option value="high">عالية</option>
                                            <option value="urgent">عاجلة</option>
                                        </select>
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">ملاحظات</label>
                                        <input type="text" class="form-input compact" name="notes" placeholder="ملاحظات...">
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">المساحة (م²)</label>
                                        <input type="number" class="form-input compact" id="total-area" readonly style="background: #e9ecef; font-weight: bold;">
                                    </div>
                                    <div class="form-group compact" style="flex: 0 0 120px;">
                                        <label class="form-label compact">التكلفة</label>
                                        <input type="number" class="form-input compact" id="total-cost" readonly style="background: #e9ecef; font-weight: bold; color: #28a745;">
                                    </div>
                                </div>

                                <!-- السطر الثاني (تحت مباشرة): جدول الأصناف -->
                                <div class="order-items-row">
                                    <div class="items-header-compact">
                                        <span class="section-title-compact">تفاصيل الأصناف:</span>
                                        <button type="button" class="btn btn-secondary btn-sm compact" onclick="addOrderItemRow()">
                                            <i class="fas fa-plus"></i> إضافة صنف
                                        </button>
                                    </div>
                                    <div class="items-table-container-compact">
                                        <table class="items-table-compact">
                                            <thead>
                                                <tr>
                                                    <th>وصف الصنف المراد دهانه</th>
                                                    <th>نوع الدهان والمواد</th>
                                                    <th>الطول (م)</th>
                                                    <th>العرض (م)</th>
                                                    <th>العدد</th>
                                                    <th>المساحة (م²)</th>
                                                    <th>السعر/م²</th>
                                                    <th>الإجمالي</th>
                                                    <th>حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="order-items-container">
                                                ${createOrderItemRow()}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>


                            </div>
                            <div class="modal-footer compact">
                                <button type="button" class="btn btn-secondary compact" onclick="closeModal()">إلغاء</button>
                                <button type="submit" class="btn compact">
                                    <i class="fas fa-save"></i> إنشاء الأمر
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.getElementById('modal-container').innerHTML = modalContent;

            // إضافة مستمعات الأحداث
            setTimeout(() => {
                const container = document.getElementById('order-items-container');
                container.addEventListener('input', calculateOrderTotals);
                container.addEventListener('change', calculateOrderTotals);
                calculateOrderTotals();
            }, 100);
        }

        function createOrderItemRow(item = {}, index = 0) {
            return `
                <tr class="order-item-row">
                    <td style="padding: 4px;">
                        <input type="text" class="form-input item-description" name="itemDescription[]" value="${item.itemDescription || ''}"
                               placeholder="وصف الصنف: باب خشبي، نافذة ألمنيوم، جدار خرساني، سقف جبس..."
                               style="width: 100%; min-width: 200px;" required>
                    </td>
                    <td style="padding: 4px;">
                        <select class="form-select paint-type-select" name="paintType[]" onchange="updatePaintPrice(this)"
                                style="width: 100%; min-width: 180px;" required>
                            <option value="">اختر نوع الدهان والمواد</option>
                            ${paintTypes.map(paint => `
                                <option value="${paint.id}" data-price="${paint.pricePerMeter || 0}" data-cost="${paint.costPerMeter || 0}" ${item.paintTypeId === paint.id ? 'selected' : ''}>
                                    ${paint.name} - ${paint.color} (${formatCurrency(paint.pricePerMeter || 0)}/م²)
                                </option>
                            `).join('')}
                        </select>
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input length-input" name="length[]" value="${item.length || ''}"
                               step="0.01" onchange="calculateItemArea(this)" placeholder="0.00"
                               style="width: 100%; min-width: 80px;" required>
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input width-input" name="width[]" value="${item.width || ''}"
                               step="0.01" onchange="calculateItemArea(this)" placeholder="0.00"
                               style="width: 100%; min-width: 80px;" required>
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input quantity-input" name="quantity[]" value="${item.quantity || 1}"
                               min="1" onchange="calculateItemArea(this)" placeholder="1"
                               style="width: 100%; min-width: 60px;" required>
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input area-input" name="totalArea[]" value="${item.totalArea || ''}"
                               step="0.01" readonly style="background: #e9ecef; font-weight: bold; width: 100%; min-width: 80px;">
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input price-input" name="pricePerMeter[]" value="${item.pricePerMeter || ''}"
                               step="0.01" readonly style="background: #e9ecef; width: 100%; min-width: 80px;">
                    </td>
                    <td style="padding: 4px;">
                        <input type="number" class="form-input total-input" name="itemTotal[]" value="${item.total || ''}"
                               readonly style="background: #e9ecef; font-weight: bold; color: #28a745; width: 100%; min-width: 90px;">
                    </td>
                    <td style="padding: 4px; text-align: center;">
                        <button type="button" class="btn btn-danger btn-sm compact" onclick="removeOrderItemRow(this)" title="حذف الصنف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        function addOrderItemRow() {
            const container = document.getElementById('order-items-container');
            const newRow = document.createElement('tr');
            newRow.innerHTML = createOrderItemRow();
            newRow.className = 'order-item-row';
            container.appendChild(newRow);
        }

        function removeOrderItemRow(button) {
            const row = button.closest('tr');
            if (document.querySelectorAll('#order-items-container tr').length > 1) {
                row.remove();
                calculateOrderTotals();
            } else {
                alert('يجب الاحتفاظ بصنف واحد على الأقل');
            }
        }

        function updatePaintPrice(select) {
            const row = select.closest('tr');
            const priceInput = row.querySelector('.price-input');
            const selectedOption = select.options[select.selectedIndex];
            const price = selectedOption.dataset.price || 0;
            priceInput.value = price;
            calculateItemTotal(priceInput);
        }

        function calculateItemArea(input) {
            const row = input.closest('tr');
            const lengthInput = row.querySelector('.length-input');
            const widthInput = row.querySelector('.width-input');
            const quantityInput = row.querySelector('.quantity-input');
            const areaInput = row.querySelector('.area-input');

            const length = parseFloat(lengthInput.value) || 0;
            const width = parseFloat(widthInput.value) || 0;
            const quantity = parseFloat(quantityInput.value) || 1;

            // حساب المساحة: الطول × العرض × العدد
            const totalArea = length * width * quantity;
            areaInput.value = totalArea.toFixed(2);

            calculateItemTotal(areaInput);
        }

        function calculateItemTotal(input) {
            const row = input.closest('tr');
            const areaInput = row.querySelector('.area-input');
            const priceInput = row.querySelector('.price-input');
            const totalInput = row.querySelector('.total-input');

            const area = parseFloat(areaInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = area * price;

            totalInput.value = total.toFixed(2);
            calculateOrderTotals();
        }

        function calculateOrderTotals() {
            const rows = document.querySelectorAll('#order-items-container tr');
            let totalArea = 0;
            let totalCost = 0;

            rows.forEach(row => {
                const areaInput = row.querySelector('.area-input');
                const totalInput = row.querySelector('.total-input');

                if (areaInput && totalInput) {
                    totalArea += parseFloat(areaInput.value) || 0;
                    totalCost += parseFloat(totalInput.value) || 0;
                }
            });

            const totalAreaElement = document.getElementById('total-area');
            const totalCostElement = document.getElementById('total-cost');

            if (totalAreaElement) totalAreaElement.value = totalArea.toFixed(2);
            if (totalCostElement) totalCostElement.value = totalCost.toFixed(2);
        }

        function saveTestOrder(event) {
            event.preventDefault();

            // جمع البيانات
            const formData = new FormData(event.target);
            const orderData = {
                orderNumber: formData.get('orderNumber'),
                customerId: formData.get('customerId'),
                orderDate: formData.get('orderDate'),
                deliveryDate: formData.get('deliveryDate'),
                status: formData.get('status'),
                priority: formData.get('priority'),
                notes: formData.get('notes'),
                totalArea: document.getElementById('total-area').value,
                totalCost: document.getElementById('total-cost').value
            };

            // جمع الأصناف (أصناف العميل المراد دهانها)
            const itemDescriptions = formData.getAll('itemDescription[]');
            const paintTypes = formData.getAll('paintType[]');
            const lengths = formData.getAll('length[]');
            const widths = formData.getAll('width[]');
            const quantities = formData.getAll('quantity[]');
            const totalAreas = formData.getAll('totalArea[]');
            const prices = formData.getAll('pricePerMeter[]');
            const totals = formData.getAll('itemTotal[]');

            orderData.items = itemDescriptions.map((itemDescription, index) => ({
                itemDescription: itemDescription.trim(), // وصف الصنف المراد دهانه (ملك العميل)
                paintTypeId: paintTypes[index], // نوع الدهان والمواد المستخدمة (من المخزون)
                length: parseFloat(lengths[index]) || 0,
                width: parseFloat(widths[index]) || 0,
                quantity: parseFloat(quantities[index]) || 1,
                totalArea: parseFloat(totalAreas[index]) || 0,
                pricePerMeter: parseFloat(prices[index]) || 0,
                total: parseFloat(totals[index]) || 0,
                isCustomerItem: true // هذا صنف العميل وليس من مخزوننا
            })).filter(item => item.itemDescription && item.totalArea > 0 && item.paintTypeId);

            console.log('بيانات الأمر:', orderData);

            closeModal();
            alert('تم حفظ أمر الإنتاج بنجاح!\nتحقق من وحدة التحكم لرؤية البيانات.');
        }

        function closeModal() {
            document.getElementById('modal-container').innerHTML = '';
        }
    </script>
</body>
</html>
