<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة أمر الإنتاج المحسنة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: #f0f2f5;
            font-family: 'Segoe UI', 'Cairo', sans-serif;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .test-header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }
        
        .feature-card h3 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .feature-card ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-card li {
            padding: 5px 0;
            color: #4a5568;
        }
        
        .feature-card li:before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .test-button {
            display: block;
            width: 300px;
            margin: 30px auto;
            padding: 15px 30px;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
        }
        
        .test-button i {
            margin-left: 10px;
        }
        
        .modal {
            display: block !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> نافذة أمر الإنتاج المحسنة</h1>
            <p>تصميم احترافي مضغوط مع تخطيط أفقي وحسابات تلقائية محسنة</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3><i class="fas fa-layout-alt"></i> التصميم المحسن</h3>
                <ul>
                    <li>تخطيط أفقي مضغوط</li>
                    <li>سطرين فوق للمعلومات الأساسية</li>
                    <li>جدول أصناف احترافي</li>
                    <li>تنسيق مضغوط وأنيق</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3><i class="fas fa-calculator"></i> الحسابات التلقائية</h3>
                <ul>
                    <li>حساب المساحة تلقائياً</li>
                    <li>حساب التكلفة الإجمالية</li>
                    <li>تحديث فوري للأسعار</li>
                    <li>ملخص تكلفة شامل</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3><i class="fas fa-shield-alt"></i> فحص الأخطاء</h3>
                <ul>
                    <li>التحقق من صحة البيانات</li>
                    <li>رسائل خطأ واضحة</li>
                    <li>تمييز الحقول الخاطئة</li>
                    <li>منع الحفظ مع أخطاء</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3><i class="fas fa-mobile-alt"></i> التصميم المتجاوب</h3>
                <ul>
                    <li>يعمل على جميع الشاشات</li>
                    <li>تخطيط متكيف</li>
                    <li>سهولة الاستخدام</li>
                    <li>واجهة احترافية</li>
                </ul>
            </div>
        </div>
        
        <button class="test-button" onclick="openTestOrderModal()">
            <i class="fas fa-play"></i>
            اختبار النافذة المحسنة
        </button>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script>
        // Sample data for testing
        const customers = [
            { id: 1, name: 'أحمد محمد العلي' },
            { id: 2, name: 'فاطمة علي السعد' },
            { id: 3, name: 'محمد السعد الأحمد' }
        ];

        const items = [
            { id: 1, name: 'دهان خشب عادي أبيض', pricePerSqm: 25, colorCode: 'RAL-9010' },
            { id: 2, name: 'ورنيش لامع شفاف', pricePerSqm: 35, colorCode: 'CLEAR' },
            { id: 3, name: 'بوية زيت بني', pricePerSqm: 30, colorCode: 'RAL-8017' },
            { id: 4, name: 'لاكيه أسود لامع', pricePerSqm: 45, colorCode: 'RAL-9005' }
        ];

        function generateOrderNumber() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `ORD-${year}${month}${day}-001`;
        }

        function showNotification(message, type = 'info') {
            alert(message);
        }

        function closeModal(modalId) {
            document.getElementById('modal-container').innerHTML = '';
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        // Include the enhanced order modal functions from script.js
        // This would normally be imported from the main script file
    </script>
    
    <script src="script.js"></script>
    
    <script>
        function openTestOrderModal() {
            openOrderModal();
        }
    </script>
</body>
</html>
