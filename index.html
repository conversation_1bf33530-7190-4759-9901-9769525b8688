<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة الدهان المتكامل</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="company-logo">
                    <i class="fas fa-paint-brush"></i>
                </div>
                <h1 id="company-name">نظام إدارة شركة الدهان</h1>
                <p>نظام متكامل لإدارة شركات دهان الأثاث الخشبي</p>
            </div>
            <form id="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" id="username" class="form-input" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" id="password" class="form-input" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </form>
            <div class="login-footer">
                <p>المطور: فارس نواف | <EMAIL> | 0569329925</p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img id="company-logo-img" src="" alt="شعار الشركة" style="display: none;">
                    <i id="company-logo-icon" class="fas fa-paint-brush"></i>
                    <div class="company-info">
                        <h1 id="header-company-name">نظام إدارة شركة الدهان</h1>
                        <span id="current-user" class="user-info"></span>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="currency-selector">
                        <select id="currency-selector" class="currency-select" onchange="changeCurrency(this.value)">
                            <option value="SAR">ريال سعودي</option>
                            <option value="USD">دولار أمريكي</option>
                            <option value="EUR">يورو</option>
                            <option value="AED">درهم إماراتي</option>
                            <option value="KWD">دينار كويتي</option>
                            <option value="QAR">ريال قطري</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" onclick="openBackupManager()" title="النسخ الاحتياطية">
                        <i class="fas fa-database"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="openImportExport()" title="استيراد/تصدير">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="openNotifications()" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count" class="notification-badge">0</span>
                    </button>
                    <button class="btn btn-secondary" onclick="openSettings()" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn btn-danger" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span id="sidebar-username" class="username"></span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-category">
                    <span class="category-title">الإدارة الرئيسية</span>
                </li>
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>
                <li><a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i> إعدادات النظام
                </a></li>
                <li><a href="#users" class="nav-link" data-section="users">
                    <i class="fas fa-users-cog"></i> إدارة المستخدمين
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة العلاقات</span>
                </li>
                <li><a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i> العملاء
                </a></li>
                <li><a href="#suppliers" class="nav-link" data-section="suppliers">
                    <i class="fas fa-truck"></i> الموردين
                </a></li>
                <li><a href="#partners" class="nav-link" data-section="partners">
                    <i class="fas fa-handshake"></i> الشركاء
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة المخزون</span>
                </li>
                <li><a href="#inventory" class="nav-link" data-section="inventory">
                    <i class="fas fa-warehouse"></i> المخازن
                </a></li>
                <li><a href="#raw-materials" class="nav-link" data-section="raw-materials">
                    <i class="fas fa-oil-can"></i> المواد الخام
                </a></li>
                <li><a href="#items" class="nav-link" data-section="items">
                    <i class="fas fa-boxes"></i> الأصناف المنتجة
                </a></li>

                <li class="nav-category">
                    <span class="category-title">العمليات</span>
                </li>
                <li><a href="#orders" class="nav-link" data-section="orders">
                    <i class="fas fa-clipboard-list"></i> أوامر الإنتاج
                </a></li>
                <li><a href="#production" class="nav-link" data-section="production">
                    <i class="fas fa-industry"></i> الإنتاج
                </a></li>
                <li><a href="#quality" class="nav-link" data-section="quality">
                    <i class="fas fa-check-circle"></i> مراقبة الجودة
                </a></li>

                <li class="nav-category">
                    <span class="category-title">المالية</span>
                </li>
                <li><a href="#invoices" class="nav-link" data-section="invoices">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a></li>
                <li><a href="#payments" class="nav-link" data-section="payments">
                    <i class="fas fa-money-bill-wave"></i> المقبوضات والمدفوعات
                </a></li>
                <li><a href="#expenses" class="nav-link" data-section="expenses">
                    <i class="fas fa-receipt"></i> المصروفات
                </a></li>
                <li><a href="#accounting" class="nav-link" data-section="accounting">
                    <i class="fas fa-calculator"></i> المحاسبة
                </a></li>

                <li class="nav-category">
                    <span class="category-title">التقارير والتحليل</span>
                </li>
                <li><a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a></li>
                <li><a href="#analytics" class="nav-link" data-section="analytics">
                    <i class="fas fa-chart-line"></i> التحليلات
                </a></li>
                <li><a href="#forecasting" class="nav-link" data-section="forecasting">
                    <i class="fas fa-crystal-ball"></i> التنبؤات
                </a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-header">
                    <h2>لوحة التحكم</h2>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-secondary" onclick="exportDashboardReport()">
                            <i class="fas fa-file-export"></i> تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-stats">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3>أوامر الإنتاج</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-orders">0</span>
                                <span class="sub-number">+<span id="new-orders">0</span> جديد</span>
                            </div>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>12%</span>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3>إجمالي المبيعات</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-sales">0</span>
                                <span class="sub-number" id="sales-currency">ريال</span>
                            </div>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>8%</span>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content">
                            <h3>المخزون</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-inventory">0</span>
                                <span class="sub-number">صنف</span>
                            </div>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="low-stock-count">0</span>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>العملاء النشطين</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="active-customers">0</span>
                                <span class="sub-number">من <span id="total-customers">0</span></span>
                            </div>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>5%</span>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Quick Actions -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>إجراءات سريعة</h3>
                        </div>
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="openOrderModal()">
                                <i class="fas fa-plus"></i>
                                <span>أمر إنتاج جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="openCustomerModal()">
                                <i class="fas fa-user-plus"></i>
                                <span>عميل جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="openInventoryModal()">
                                <i class="fas fa-box"></i>
                                <span>إضافة للمخزون</span>
                            </button>
                            <button class="quick-action-btn" onclick="openPaymentModal()">
                                <i class="fas fa-money-bill"></i>
                                <span>تسجيل دفعة</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>الأنشطة الحديثة</h3>
                            <button class="btn btn-sm" onclick="viewAllActivities()">عرض الكل</button>
                        </div>
                        <div class="activity-list" id="recent-activities">
                            <!-- Activities will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Pending Orders -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>أوامر في الانتظار</h3>
                            <span class="widget-count" id="pending-orders-count">0</span>
                        </div>
                        <div class="pending-orders-list" id="pending-orders">
                            <!-- Pending orders will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Low Stock Alert -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>تنبيهات المخزون</h3>
                            <span class="widget-count alert" id="low-stock-alert-count">0</span>
                        </div>
                        <div class="low-stock-list" id="low-stock-items">
                            <!-- Low stock items will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Sales Chart -->
                    <div class="dashboard-widget chart-widget">
                        <div class="widget-header">
                            <h3>مبيعات الشهر</h3>
                            <select class="form-select-sm" onchange="updateSalesChart(this.value)">
                                <option value="month">هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                        <div class="chart-container" id="sales-chart">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>

                    <!-- Top Customers -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>أفضل العملاء</h3>
                        </div>
                        <div class="top-customers-list" id="top-customers">
                            <!-- Top customers will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <div class="section-header">
                    <h2>إعدادات النظام</h2>
                </div>

                <div class="settings-container">
                    <div class="settings-sidebar">
                        <ul class="settings-menu">
                            <li><a href="#company-settings" class="settings-link active" data-tab="company">
                                <i class="fas fa-building"></i> معلومات الشركة
                            </a></li>
                            <li><a href="#currency-settings" class="settings-link" data-tab="currency">
                                <i class="fas fa-coins"></i> إعدادات العملة
                            </a></li>
                            <li><a href="#system-settings" class="settings-link" data-tab="system">
                                <i class="fas fa-cogs"></i> إعدادات النظام
                            </a></li>
                            <li><a href="#notification-settings" class="settings-link" data-tab="notifications">
                                <i class="fas fa-bell"></i> الإشعارات
                            </a></li>
                            <li><a href="#backup-settings" class="settings-link" data-tab="backup">
                                <i class="fas fa-database"></i> النسخ الاحتياطية
                            </a></li>
                            <li><a href="#security-settings" class="settings-link" data-tab="security">
                                <i class="fas fa-shield-alt"></i> الأمان
                            </a></li>
                        </ul>
                    </div>

                    <div class="settings-content">
                        <!-- Company Settings -->
                        <div id="company-settings-tab" class="settings-tab active">
                            <h3>معلومات الشركة</h3>
                            <form id="company-settings-form" onsubmit="saveCompanySettings(event)">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-input" name="companyName" id="company-name-input" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">الاسم التجاري</label>
                                        <input type="text" class="form-input" name="tradeName" id="trade-name-input">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">رقم السجل التجاري</label>
                                        <input type="text" class="form-input" name="commercialRegister" id="commercial-register-input">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-input" name="taxNumber" id="tax-number-input">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-textarea" name="address" id="company-address-input"></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-input" name="phone" id="company-phone-input">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-input" name="email" id="company-email-input">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">شعار الشركة</label>
                                    <div class="logo-upload">
                                        <input type="file" id="logo-upload" accept="image/*" onchange="handleLogoUpload(event)">
                                        <div class="logo-preview" id="logo-preview">
                                            <i class="fas fa-image"></i>
                                            <span>اختر شعار الشركة</span>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </form>
                        </div>

                        <!-- Currency Settings -->
                        <div id="currency-settings-tab" class="settings-tab">
                            <h3>إعدادات العملة</h3>
                            <form id="currency-settings-form" onsubmit="saveCurrencySettings(event)">
                                <div class="form-group">
                                    <label class="form-label">العملة الأساسية</label>
                                    <select class="form-select" name="baseCurrency" id="base-currency-select">
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                        <option value="QAR">ريال قطري (QAR)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">أسعار الصرف</label>
                                    <div class="exchange-rates" id="exchange-rates">
                                        <!-- Exchange rates will be populated by JavaScript -->
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">تحديث أسعار الصرف تلقائياً</label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="auto-update-rates" name="autoUpdateRates">
                                        <label for="auto-update-rates" class="toggle-label"></label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </form>
                        </div>

                        <!-- System Settings -->
                        <div id="system-settings-tab" class="settings-tab">
                            <h3>إعدادات النظام</h3>
                            <form id="system-settings-form" onsubmit="saveSystemSettings(event)">
                                <div class="form-group">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" name="timezone" id="timezone-select">
                                        <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                        <option value="Asia/Qatar">قطر (GMT+3)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" name="dateFormat" id="date-format-select">
                                        <option value="DD/MM/YYYY">يوم/شهر/سنة</option>
                                        <option value="MM/DD/YYYY">شهر/يوم/سنة</option>
                                        <option value="YYYY-MM-DD">سنة-شهر-يوم</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">لغة النظام</label>
                                    <select class="form-select" name="language" id="language-select">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">حد تنبيه المخزون المنخفض</label>
                                    <input type="number" class="form-input" name="lowStockThreshold" id="low-stock-threshold" min="1" value="10">
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Import/Export Section -->
            <section id="import-export" class="content-section">
                <div class="section-header">
                    <h2>استيراد وتصدير البيانات</h2>
                </div>

                <div class="import-export-container">
                    <div class="import-export-grid">
                        <!-- Export Section -->
                        <div class="export-section">
                            <h3><i class="fas fa-download"></i> تصدير البيانات</h3>

                            <div class="export-options">
                                <div class="export-option">
                                    <h4>تصدير شامل</h4>
                                    <p>تصدير جميع البيانات في ملف واحد</p>
                                    <button class="btn btn-primary" onclick="exportAllData()">
                                        <i class="fas fa-database"></i> تصدير جميع البيانات
                                    </button>
                                </div>

                                <div class="export-option">
                                    <h4>تصدير العملاء</h4>
                                    <p>تصدير بيانات العملاء إلى Excel</p>
                                    <button class="btn btn-success" onclick="exportToExcel('customers')">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </button>
                                </div>

                                <div class="export-option">
                                    <h4>تصدير الأصناف</h4>
                                    <p>تصدير بيانات الأصناف إلى Excel</p>
                                    <button class="btn btn-success" onclick="exportToExcel('items')">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </button>
                                </div>

                                <div class="export-option">
                                    <h4>تصدير الموردين</h4>
                                    <p>تصدير بيانات الموردين إلى Excel</p>
                                    <button class="btn btn-success" onclick="exportToExcel('suppliers')">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </button>
                                </div>

                                <div class="export-option">
                                    <h4>تصدير الشركاء</h4>
                                    <p>تصدير بيانات الشركاء إلى Excel</p>
                                    <button class="btn btn-success" onclick="exportToExcel('partners')">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </button>
                                </div>

                                <div class="export-option">
                                    <h4>تصدير المخزون</h4>
                                    <p>تصدير بيانات المخزون إلى Excel</p>
                                    <button class="btn btn-success" onclick="exportToExcel('inventory')">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Import Section -->
                        <div class="import-section">
                            <h3><i class="fas fa-upload"></i> استيراد البيانات</h3>

                            <div class="import-options">
                                <div class="import-option">
                                    <h4>استيراد من Excel</h4>
                                    <p>استيراد البيانات من ملفات Excel</p>

                                    <div class="import-controls">
                                        <select id="import-type" class="form-select">
                                            <option value="">اختر نوع البيانات</option>
                                            <option value="customers">العملاء</option>
                                            <option value="suppliers">الموردين</option>
                                            <option value="items">الأصناف</option>
                                            <option value="partners">الشركاء</option>
                                            <option value="inventory">المخزون</option>
                                            <option value="raw-materials">المواد الخام</option>
                                        </select>

                                        <input type="file" id="excel-file-input" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleExcelImport(event)">
                                        <button class="btn btn-primary" onclick="document.getElementById('excel-file-input').click()">
                                            <i class="fas fa-file-excel"></i> اختر ملف Excel
                                        </button>
                                    </div>

                                    <div class="import-preview" id="import-preview" style="display: none;">
                                        <h5>معاينة البيانات:</h5>
                                        <div id="preview-table"></div>
                                        <div class="import-actions">
                                            <button class="btn btn-success" onclick="confirmImport()">
                                                <i class="fas fa-check"></i> تأكيد الاستيراد
                                            </button>
                                            <button class="btn btn-secondary" onclick="cancelImport()">
                                                <i class="fas fa-times"></i> إلغاء
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="import-option">
                                    <h4>استيراد نسخة احتياطية</h4>
                                    <p>استيراد نسخة احتياطية كاملة</p>

                                    <input type="file" id="backup-import-input" accept=".json" style="display: none;" onchange="handleBackupImport(event)">
                                    <button class="btn btn-warning" onclick="document.getElementById('backup-import-input').click()">
                                        <i class="fas fa-database"></i> استيراد نسخة احتياطية
                                    </button>
                                </div>

                                <div class="import-templates">
                                    <h4>تحميل قوالب Excel</h4>
                                    <p>قم بتحميل القوالب لضمان التنسيق الصحيح</p>

                                    <div class="template-buttons">
                                        <button class="btn btn-secondary btn-sm" onclick="downloadTemplate('customers')">
                                            <i class="fas fa-download"></i> قالب العملاء
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="downloadTemplate('suppliers')">
                                            <i class="fas fa-download"></i> قالب الموردين
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="downloadTemplate('items')">
                                            <i class="fas fa-download"></i> قالب الأصناف
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="downloadTemplate('partners')">
                                            <i class="fas fa-download"></i> قالب الشركاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Import History -->
                    <div class="import-history">
                        <h3>سجل عمليات الاستيراد</h3>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>نوع البيانات</th>
                                        <th>عدد السجلات</th>
                                        <th>المستخدم</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="import-history-table">
                                    <!-- Import history will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="content-section">
                <div class="section-header">
                    <h2>إدارة العملاء</h2>
                    <button class="btn btn-primary" onclick="openCustomerModal()">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table">
                            <!-- Customer data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Suppliers Section -->
            <section id="suppliers" class="content-section">
                <div class="section-header">
                    <h2>إدارة الموردين</h2>
                    <button class="btn btn-primary" onclick="openSupplierModal()">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table">
                            <!-- Supplier data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Inventory Section -->
            <section id="inventory" class="content-section">
                <div class="section-header">
                    <h2>إدارة المخازن</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="openWarehouseModal()">
                            <i class="fas fa-plus"></i> إضافة مخزن
                        </button>
                        <button class="btn btn-secondary" onclick="openInventoryTransferModal()">
                            <i class="fas fa-exchange-alt"></i> نقل مخزون
                        </button>
                        <button class="btn btn-warning" onclick="openStockAdjustmentModal()">
                            <i class="fas fa-edit"></i> تعديل مخزون
                        </button>
                    </div>
                </div>

                <div class="inventory-overview">
                    <div class="inventory-stats">
                        <div class="stat-item">
                            <i class="fas fa-warehouse"></i>
                            <div>
                                <span class="stat-number" id="total-warehouses">0</span>
                                <span class="stat-label">مخزن</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-boxes"></i>
                            <div>
                                <span class="stat-number" id="total-stock-items">0</span>
                                <span class="stat-label">صنف</span>
                            </div>
                        </div>
                        <div class="stat-item alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div>
                                <span class="stat-number" id="low-stock-items-count">0</span>
                                <span class="stat-label">مخزون منخفض</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="inventory-filters">
                    <div class="filter-group">
                        <label>المخزن:</label>
                        <select class="form-select" id="warehouse-filter" onchange="filterInventory()">
                            <option value="">جميع المخازن</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الفئة:</label>
                        <select class="form-select" id="category-filter" onchange="filterInventory()">
                            <option value="">جميع الفئات</option>
                            <option value="paint">دهانات</option>
                            <option value="thinner">مذيبات</option>
                            <option value="tools">أدوات</option>
                            <option value="accessories">إكسسوارات</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select class="form-select" id="status-filter" onchange="filterInventory()">
                            <option value="">جميع الحالات</option>
                            <option value="available">متوفر</option>
                            <option value="low">مخزون منخفض</option>
                            <option value="out">نفد المخزون</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم المادة</th>
                                <th>الفئة</th>
                                <th>المخزن</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>الوحدة</th>
                                <th>آخر حركة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="inventory-table">
                            <!-- Inventory data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Raw Materials Section -->
            <section id="raw-materials" class="content-section">
                <div class="section-header">
                    <h2>إدارة المواد الخام</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="openRawMaterialModal()">
                            <i class="fas fa-plus"></i> إضافة مادة خام
                        </button>
                        <button class="btn btn-secondary" onclick="openPurchaseOrderModal()">
                            <i class="fas fa-shopping-cart"></i> أمر شراء
                        </button>
                        <button class="btn btn-success" onclick="openReceiptModal()">
                            <i class="fas fa-truck-loading"></i> استلام بضاعة
                        </button>
                    </div>
                </div>

                <div class="materials-categories">
                    <div class="category-tabs">
                        <button class="category-tab active" data-category="all" onclick="filterMaterialsByCategory('all')">
                            جميع المواد
                        </button>
                        <button class="category-tab" data-category="paint" onclick="filterMaterialsByCategory('paint')">
                            دهانات
                        </button>
                        <button class="category-tab" data-category="thinner" onclick="filterMaterialsByCategory('thinner')">
                            مذيبات
                        </button>
                        <button class="category-tab" data-category="primer" onclick="filterMaterialsByCategory('primer')">
                            برايمر
                        </button>
                        <button class="category-tab" data-category="varnish" onclick="filterMaterialsByCategory('varnish')">
                            ورنيش
                        </button>
                        <button class="category-tab" data-category="tools" onclick="filterMaterialsByCategory('tools')">
                            أدوات
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم المادة</th>
                                <th>الفئة</th>
                                <th>المورد</th>
                                <th>الكمية المتوفرة</th>
                                <th>الوحدة</th>
                                <th>سعر الوحدة</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="raw-materials-table">
                            <!-- Raw materials data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Items Section -->
            <section id="items" class="content-section">
                <div class="section-header">
                    <h2>إدارة الأصناف المنتجة</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" onclick="openItemModal()">
                            <i class="fas fa-plus"></i> إضافة صنف جديد
                        </button>
                        <button class="btn btn-secondary" onclick="openRecipeModal()">
                            <i class="fas fa-list-ul"></i> إدارة الوصفات
                        </button>
                        <button class="btn btn-success" onclick="openPriceListModal()">
                            <i class="fas fa-tags"></i> قائمة الأسعار
                        </button>
                    </div>
                </div>

                <div class="items-filters">
                    <div class="filter-group">
                        <label>نوع الدهان:</label>
                        <select class="form-select" id="paint-type-filter" onchange="filterItems()">
                            <option value="">جميع الأنواع</option>
                            <option value="دهان عادي">دهان عادي</option>
                            <option value="دهان لامع">دهان لامع</option>
                            <option value="دهان مطفي">دهان مطفي</option>
                            <option value="ورنيش">ورنيش</option>
                            <option value="بوية زيت">بوية زيت</option>
                            <option value="لاكيه">لاكيه</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الفئة السعرية:</label>
                        <select class="form-select" id="price-range-filter" onchange="filterItems()">
                            <option value="">جميع الفئات</option>
                            <option value="low">اقتصادي (أقل من 50)</option>
                            <option value="medium">متوسط (50-100)</option>
                            <option value="high">مرتفع (أكثر من 100)</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الصنف</th>
                                <th>نوع الدهان</th>
                                <th>رقم اللون</th>
                                <th>سعر المتر المربع</th>
                                <th>الوحدة</th>
                                <th>وقت التجفيف</th>
                                <th>التغطية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="items-table">
                            <!-- Items data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders" class="content-section">
                <div class="section-header">
                    <h2>أوامر الإنتاج</h2>
                    <button class="btn btn-primary" onclick="openOrderModal()">
                        <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الأمر</th>
                                <th>العميل</th>
                                <th>تاريخ الأمر</th>
                                <th>تاريخ التسليم</th>
                                <th>الحالة</th>
                                <th>إجمالي التكلفة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- Orders data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Invoices Section -->
            <section id="invoices" class="content-section">
                <div class="section-header">
                    <h2>الفواتير</h2>
                    <button class="btn btn-primary" onclick="convertOrderToInvoice()">
                        <i class="fas fa-exchange-alt"></i> تحويل أمر إنتاج لفاتورة
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الفاتورة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoices-table">
                            <!-- Invoices data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Payments Section -->
            <section id="payments" class="content-section">
                <div class="section-header">
                    <h2>المقبوضات والمدفوعات</h2>
                    <button class="btn btn-primary" onclick="openPaymentModal()">
                        <i class="fas fa-plus"></i> إضافة عملية دفع
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>النوع</th>
                                <th>العميل/المورد</th>
                                <th>المبلغ</th>
                                <th>العملة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="payments-table">
                            <!-- Payments data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Partners Section -->
            <section id="partners" class="content-section">
                <div class="section-header">
                    <h2>إدارة الشركاء</h2>
                    <button class="btn btn-primary" onclick="openPartnerModal()">
                        <i class="fas fa-plus"></i> إضافة شريك جديد
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الشريك</th>
                                <th>نسبة الشراكة (%)</th>
                                <th>رأس المال</th>
                                <th>الأرباح المستحقة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="partners-table">
                            <!-- Partners data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Reports Section -->
            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2>التقارير</h2>
                </div>
                <div class="reports-grid">
                    <div class="report-card" onclick="generateSalesReport()">
                        <i class="fas fa-chart-line"></i>
                        <h3>تقرير المبيعات</h3>
                        <p>تقرير شامل عن المبيعات والإيرادات</p>
                    </div>
                    <div class="report-card" onclick="generateCustomersReport()">
                        <i class="fas fa-users"></i>
                        <h3>تقرير العملاء</h3>
                        <p>تقرير عن أرصدة ومعاملات العملاء</p>
                    </div>
                    <div class="report-card" onclick="generateCustomerBalanceReport()">
                        <i class="fas fa-balance-scale"></i>
                        <h3>تقرير أرصدة العملاء</h3>
                        <p>تقرير مفصل عن أرصدة العملاء والمديونيات</p>
                    </div>
                    <div class="report-card" onclick="testCustomerBalanceReport()" style="border: 2px solid #e53e3e;">
                        <i class="fas fa-bug"></i>
                        <h3>اختبار التقرير</h3>
                        <p>اختبار تقرير أرصدة العملاء</p>
                    </div>
                    <div class="report-card" onclick="generateSuppliersReport()">
                        <i class="fas fa-truck"></i>
                        <h3>تقرير الموردين</h3>
                        <p>تقرير عن أرصدة ومعاملات الموردين</p>
                    </div>
                    <div class="report-card" onclick="generateProfitReport()">
                        <i class="fas fa-money-bill-wave"></i>
                        <h3>تقرير الأرباح</h3>
                        <p>تقرير توزيع الأرباح على الشركاء</p>
                    </div>
                    <div class="report-card" onclick="generateInventoryReport()">
                        <i class="fas fa-boxes"></i>
                        <h3>تقرير المخزون</h3>
                        <p>تقرير شامل عن حالة المخزون والكميات</p>
                    </div>
                    <div class="report-card" onclick="generateProductionReport()">
                        <i class="fas fa-cogs"></i>
                        <h3>تقرير الإنتاج</h3>
                        <p>تقرير أوامر الإنتاج وحالة التنفيذ</p>
                    </div>
                    <div class="report-card" onclick="generatePaymentsReport()">
                        <i class="fas fa-credit-card"></i>
                        <h3>تقرير المدفوعات</h3>
                        <p>تقرير المدفوعات وطرق الدفع</p>
                    </div>
                    <div class="report-card" onclick="generateExpensesReport()">
                        <i class="fas fa-receipt"></i>
                        <h3>تقرير المصروفات</h3>
                        <p>تقرير المصروفات والتكاليف التشغيلية</p>
                    </div>
                    <div class="report-card" onclick="generatePartnersReport()">
                        <i class="fas fa-handshake"></i>
                        <h3>تقرير الشركاء</h3>
                        <p>تقرير الشركاء ونسب الأرباح</p>
                    </div>
                    <div class="report-card" onclick="generateFinancialReport()">
                        <i class="fas fa-chart-pie"></i>
                        <h3>التقرير المالي الشامل</h3>
                        <p>تقرير مالي شامل ومفصل للشركة</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals will be added here -->
    <div id="modal-container"></div>

    <script src="script.js"></script>
</body>
</html>
