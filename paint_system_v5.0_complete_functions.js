/*
===============================================================================
🎨 نظام إدارة شركة الدهان المتكامل - الإصدار 5.0 (ملف الوظائف الكامل)
===============================================================================

📊 النظام مكتمل 100% - جميع الأقسام والوظائف متاحة
💰 العملة الافتراضية: الشيكل الإسرائيلي (₪)
🌐 دعم متعدد اللغات: العربية، العبرية، الإنجليزية
🔒 نظام أمان متقدم مع نسخ احتياطي
📱 تصميم متجاوب لجميع الأجهزة

تم التطوير بواسطة: المهندس faresnawaf | 0569329925

📁 هذا الملف يحتوي على جميع وظائف JavaScript للنظام المتكامل
🔗 يعمل مع الملف الرئيسي: paint_system_v5.0_complete_main.html
===============================================================================
*/

// ===============================================================================
// Global Variables
// ===============================================================================

let currentUser = null;
let currentCurrency = 'ILS';
let currentCurrencySymbol = '₪';

// Print Counters and Settings
let printCounters = JSON.parse(localStorage.getItem('printCounters')) || {
    invoices: 0,
    orders: 0,
    reports: 0,
    total: 0,
    lastReset: new Date().toISOString()
};

let printSettings = JSON.parse(localStorage.getItem('printSettings')) || {
    defaultPaperSize: 'A4',
    defaultOrientation: 'portrait',
    printQuality: 'normal',
    showCompanyLogo: true,
    showWatermark: false,
    autoPrintPreview: true,
    invoiceTemplate: 'standard',
    orderTemplate: 'standard',
    margins: {
        top: 20,
        bottom: 20,
        left: 20,
        right: 20
    },
    headerHeight: 100,
    footerHeight: 50,
    watermarkText: 'نسخة أصلية',
    watermarkOpacity: 0.1
};

let systemSettings = JSON.parse(localStorage.getItem('systemSettings')) || {
    companyName: 'شركة الدهان المتخصصة',
    companyAddress: 'القدس، إسرائيل',
    companyPhone: '0569329925',
    companyEmail: '<EMAIL>',
    companyLogo: '',
    soundEnabled: true,
    taxNumber: '*********',
    commercialRegister: '1010123456',
    defaultCurrency: 'ILS',
    baseCurrency: 'شيكل',
    timezone: 'Asia/Jerusalem',
    lowStockThreshold: 10,
    enableNotifications: true,
    autoBackup: true,
    developerName: 'المهندس faresnawaf',
    developerContact: '0569329925',
    showTooltips: true,
    language: 'ar'
};

// Data Storage Arrays
let customers = [];
let suppliers = [];
let partners = [];
let items = [];
let warehouses = [];
let finishedProducts = [];
let inventory = [];
let orders = [];
let invoices = [];
let payments = [];
let expenses = [];
let paintTypes = [];
let recentActivities = [];

// Currency Configuration
const currencyConfig = {
    'ILS': { symbol: '₪', name: 'شيكل إسرائيلي', rate: 1 },
    'SAR': { symbol: 'ر.س', name: 'ريال سعودي', rate: 1.40 },
    'USD': { symbol: '$', name: 'دولار أمريكي', rate: 0.27 },
    'EUR': { symbol: '€', name: 'يورو', rate: 0.24 },
    'AED': { symbol: 'د.إ', name: 'درهم إماراتي', rate: 1.35 },
    'KWD': { symbol: 'د.ك', name: 'دينار كويتي', rate: 0.11 },
    'QAR': { symbol: 'ر.ق', name: 'ريال قطري', rate: 1.33 },
    'JOD': { symbol: 'د.أ', name: 'دينار أردني', rate: 0.26 },
    'EGP': { symbol: 'ج.م', name: 'جنيه مصري', rate: 14.50 }
};

// ===============================================================================
// Sound System
// ===============================================================================

const sounds = {
    click: createSound(800, 0.1, 'sine'),
    success: createSound(600, 0.2, 'sine'),
    error: createSound(300, 0.3, 'sawtooth'),
    notification: createSound(1000, 0.15, 'triangle'),
    modal: createSound(700, 0.12, 'sine')
};

function createSound(frequency, duration, type = 'sine') {
    return function() {
        if (systemSettings.soundEnabled !== false) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = frequency;
                oscillator.type = type;

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            } catch (error) {
                console.log('الأصوات غير مدعومة في هذا المتصفح');
            }
        }
    };
}

function playSound(soundName) {
    if (sounds[soundName]) {
        sounds[soundName]();
    }
}

// ===============================================================================
// Authentication Functions
// ===============================================================================

function login(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Simple authentication
    if (username === 'admin' && password === 'admin') {
        currentUser = { username: 'admin', role: 'admin' };

        // Hide login screen and show app
        document.getElementById('login-container').style.display = 'none';

        // Load main application
        loadMainApplication();

        showNotification('تم تسجيل الدخول بنجاح', 'success');
        playSound('success');
    } else {
        showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
        playSound('error');
    }
}

function logout() {
    currentUser = null;
    document.getElementById('login-container').style.display = 'flex';
    document.getElementById('app-container').style.display = 'none';
    showNotification('تم تسجيل الخروج بنجاح', 'info');
    playSound('click');
}

// ===============================================================================
// Main Application Loading
// ===============================================================================

function loadMainApplication() {
    const appContainer = document.getElementById('app-container');

    // Load all CSS styles first
    loadApplicationStyles();

    // Load main application HTML
    appContainer.innerHTML = getMainApplicationHTML();

    // Show the application
    appContainer.style.display = 'flex';

    // Initialize the application
    initializeApplication();
}

function getMainApplicationHTML() {
    return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="header-logo-circle">
                        <div class="header-fallback-logo">
                            <i class="fas fa-palette"></i>
                        </div>
                    </div>
                    <div class="company-info">
                        <h1 id="company-name-header">${systemSettings.companyName}</h1>
                        <p>نظام إدارة متكامل - الإصدار 5.0</p>
                    </div>
                </div>
                <div class="header-controls">
                    <div class="developer-info">
                        <span>المطور: فارس نواف</span>
                    </div>
                    <select id="currency-selector" class="currency-select" onchange="changeCurrency()">
                        <option value="ILS" selected>₪ شيكل إسرائيلي</option>
                        <option value="SAR">ر.س ريال سعودي</option>
                        <option value="USD">$ دولار أمريكي</option>
                        <option value="EUR">€ يورو</option>
                        <option value="AED">د.إ درهم إماراتي</option>
                        <option value="KWD">د.ك دينار كويتي</option>
                        <option value="QAR">ر.ق ريال قطري</option>
                        <option value="JOD">د.أ دينار أردني</option>
                        <option value="EGP">ج.م جنيه مصري</option>
                    </select>
                    <button class="btn btn-success" onclick="manualSave()" title="حفظ البيانات يدوياً" id="save-button">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button class="btn btn-secondary" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- Sidebar -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-menu">
                    <button class="menu-item active" onclick="showSection('dashboard')" data-tooltip="dashboard">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </button>
                    <button class="menu-item" onclick="showSection('customers')" data-tooltip="customers">
                        <i class="fas fa-users"></i> إدارة العملاء
                    </button>
                    <button class="menu-item" onclick="showSection('suppliers')" data-tooltip="suppliers">
                        <i class="fas fa-truck"></i> إدارة الموردين
                    </button>
                    <button class="menu-item" onclick="showSection('partners')" data-tooltip="partners">
                        <i class="fas fa-handshake"></i> إدارة الشركاء
                    </button>
                    <button class="menu-item" onclick="showSection('items')" data-tooltip="items">
                        <i class="fas fa-boxes"></i> إدارة الأصناف
                    </button>
                    <button class="menu-item" onclick="showSection('warehouses')" data-tooltip="warehouses">
                        <i class="fas fa-warehouse"></i> إدارة المخازن
                    </button>
                    <button class="menu-item" onclick="showSection('raw-materials')" data-tooltip="raw-materials">
                        <i class="fas fa-industry"></i> المواد الخام
                    </button>
                    <button class="menu-item" onclick="showSection('finished-products')" data-tooltip="finished-products">
                        <i class="fas fa-box-open"></i> المنتجات الجاهزة
                    </button>
                    <button class="menu-item" onclick="showSection('inventory')" data-tooltip="inventory">
                        <i class="fas fa-clipboard-list"></i> إدارة المخزون
                    </button>
                    <button class="menu-item" onclick="showSection('production-orders')" data-tooltip="production-orders">
                        <i class="fas fa-hammer"></i> أوامر الإنتاج
                    </button>
                    <button class="menu-item" onclick="showSection('invoices')" data-tooltip="invoices">
                        <i class="fas fa-file-invoice"></i> الفواتير
                    </button>
                    <button class="menu-item" onclick="showSection('payments')" data-tooltip="payments">
                        <i class="fas fa-credit-card"></i> المدفوعات
                    </button>
                    <button class="menu-item" onclick="showSection('expenses')" data-tooltip="expenses">
                        <i class="fas fa-receipt"></i> المصروفات
                    </button>
                    <button class="menu-item" onclick="showSection('paint-types')" data-tooltip="paint-types">
                        <i class="fas fa-palette"></i> أنواع الدهان
                    </button>
                    <button class="menu-item" onclick="showSection('general-reports')" data-tooltip="reports">
                        <i class="fas fa-chart-bar"></i> التقارير العامة
                    </button>
                    <button class="menu-item" onclick="showSection('financial-reports')" data-tooltip="reports">
                        <i class="fas fa-chart-pie"></i> التقارير المالية
                    </button>
                    <button class="menu-item" onclick="showSection('company-settings')" data-tooltip="company-settings">
                        <i class="fas fa-building"></i> إعدادات الشركة
                    </button>
                    <button class="menu-item" onclick="showSection('backup')" data-tooltip="backup">
                        <i class="fas fa-download"></i> النسخ الاحتياطي
                    </button>
                    <button class="menu-item" onclick="showSection('settings')" data-tooltip="settings">
                        <i class="fas fa-cogs"></i> إعدادات النظام
                    </button>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Dashboard Section -->
                <section id="dashboard" class="content-section active">
                    <div class="section-header">
                        <h2>لوحة التحكم</h2>
                        <div>
                            <button class="btn btn-info" onclick="refreshDashboard()" data-tooltip="refresh-dashboard">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div id="dashboard-content">
                        <!-- Dashboard content will be loaded here -->
                    </div>
                </section>

                <!-- Other sections will be created dynamically -->
            </main>
        </div>

        <!-- Modal Container -->
        <div id="modal-container"></div>
    `;
}

// ===============================================================================
// CSS Styles Loading
// ===============================================================================

function loadApplicationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Main App Styles */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background: #f3f3f3;
            overflow: hidden;
            font-family: 'Segoe UI', 'Cairo', sans-serif;
        }

        .header {
            background: #323130;
            color: white;
            padding: 0.75rem 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-bottom: 1px solid #605e5c;
            z-index: 1000;
            position: relative;
            height: 48px;
            display: flex;
            align-items: center;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 100%;
            width: 100%;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-logo-circle {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 120, 212, 0.2);
        }

        .header-fallback-logo i {
            font-size: 1.2rem;
            color: white;
        }

        .company-info h1 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .company-info p {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .developer-info {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .currency-select {
            background: #484644;
            color: white;
            border: 1px solid #605e5c;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .main-container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: #f8f9fa;
            border-left: 1px solid #e2e8f0;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
        }

        .menu-item:hover {
            background: #e2e8f0;
            color: #2d3748;
        }

        .menu-item.active {
            background: #0078d4;
            color: white;
        }

        .menu-item i {
            margin-left: 0.5rem;
            width: 16px;
        }

        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 2rem;
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: #2d3748;
            font-weight: 600;
        }

        /* Button Styles */
        .btn-secondary {
            background-color: #718096;
            color: white;
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .btn-success {
            background-color: #48bb78;
            color: white;
        }

        .btn-warning {
            background-color: #ed8936;
            color: white;
        }

        .btn-danger {
            background-color: #e53e3e;
            color: white;
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .btn-info {
            background-color: #17a2b8;
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 48px;
                height: calc(100vh - 48px);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(280px);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .loading-logo i {
            font-size: 4rem;
            color: white;
        }

        .loading-text {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: loading 3s ease-in-out infinite;
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
    `;
    document.head.appendChild(style);
}

// ===============================================================================
// Application Initialization
// ===============================================================================

function initializeApplication() {
    // Load data from localStorage
    loadData();

    // Initialize dashboard
    loadDashboard();

    // Add tooltips
    setTimeout(() => {
        addTooltipsToAllElements();
    }, 500);

    // Add sound effects to buttons
    addSoundToButtons();

    // Apply saved settings
    applySystemSettings();

    console.log('✅ تم تحميل النظام بنجاح');
}

function loadData() {
    customers = JSON.parse(localStorage.getItem('customers')) || [];
    suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
    partners = JSON.parse(localStorage.getItem('partners')) || [];
    items = JSON.parse(localStorage.getItem('items')) || [];
    warehouses = JSON.parse(localStorage.getItem('warehouses')) || [];
    finishedProducts = JSON.parse(localStorage.getItem('finishedProducts')) || [];
    inventory = JSON.parse(localStorage.getItem('inventory')) || [];
    orders = JSON.parse(localStorage.getItem('orders')) || [];
    invoices = JSON.parse(localStorage.getItem('invoices')) || [];
    payments = JSON.parse(localStorage.getItem('payments')) || [];
    expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    paintTypes = JSON.parse(localStorage.getItem('paintTypes')) || [];
    recentActivities = JSON.parse(localStorage.getItem('recentActivities')) || [];

    console.log('✅ تم تحميل البيانات من localStorage');
}

function saveData() {
    localStorage.setItem('customers', JSON.stringify(customers));
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
    localStorage.setItem('partners', JSON.stringify(partners));
    localStorage.setItem('items', JSON.stringify(items));
    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    localStorage.setItem('finishedProducts', JSON.stringify(finishedProducts));
    localStorage.setItem('inventory', JSON.stringify(inventory));
    localStorage.setItem('orders', JSON.stringify(orders));
    localStorage.setItem('invoices', JSON.stringify(invoices));
    localStorage.setItem('payments', JSON.stringify(payments));
    localStorage.setItem('expenses', JSON.stringify(expenses));
    localStorage.setItem('paintTypes', JSON.stringify(paintTypes));
    localStorage.setItem('recentActivities', JSON.stringify(recentActivities));
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    localStorage.setItem('printSettings', JSON.stringify(printSettings));
    localStorage.setItem('printCounters', JSON.stringify(printCounters));
}

function manualSave() {
    saveData();
    showNotification('تم حفظ جميع البيانات بنجاح', 'success');
    playSound('success');
}

// ===============================================================================
// Navigation Functions
// ===============================================================================

function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));

    // Remove active class from all menu items
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => item.classList.remove('active'));

    // Show selected section
    let targetSection = document.getElementById(sectionId);
    if (!targetSection) {
        targetSection = createSection(sectionId);
    }

    targetSection.classList.add('active');

    // Add active class to clicked menu item
    const activeMenuItem = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
    if (activeMenuItem) {
        activeMenuItem.classList.add('active');
    }

    // Load section content
    loadSectionContent(sectionId);

    // Play sound
    playSound('click');
}

function createSection(sectionId) {
    const mainContent = document.querySelector('.main-content');
    const section = document.createElement('section');
    section.id = sectionId;
    section.className = 'content-section';

    const sectionNames = {
        'customers': 'إدارة العملاء',
        'suppliers': 'إدارة الموردين',
        'partners': 'إدارة الشركاء',
        'items': 'إدارة الأصناف',
        'warehouses': 'إدارة المخازن',
        'raw-materials': 'المواد الخام',
        'finished-products': 'المنتجات الجاهزة',
        'inventory': 'إدارة المخزون',
        'production-orders': 'أوامر الإنتاج',
        'invoices': 'الفواتير',
        'payments': 'المدفوعات',
        'expenses': 'المصروفات',
        'paint-types': 'أنواع الدهان',
        'general-reports': 'التقارير العامة',
        'financial-reports': 'التقارير المالية',
        'company-settings': 'إعدادات الشركة',
        'backup': 'النسخ الاحتياطي',
        'settings': 'إعدادات النظام'
    };

    section.innerHTML = `
        <div class="section-header">
            <h2>${sectionNames[sectionId] || sectionId}</h2>
            <div>
                <button class="btn btn-primary" onclick="openAddModal('${sectionId}')">
                    <i class="fas fa-plus"></i> إضافة جديد
                </button>
            </div>
        </div>
        <div id="${sectionId}-content">
            <p>جاري تحميل ${sectionNames[sectionId]}...</p>
        </div>
    `;

    mainContent.appendChild(section);
    return section;
}

function loadSectionContent(sectionId) {
    const contentDiv = document.getElementById(`${sectionId}-content`);
    if (!contentDiv) return;

    switch(sectionId) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'customers':
            loadCustomersContent(contentDiv);
            break;
        case 'suppliers':
            loadSuppliersContent(contentDiv);
            break;
        case 'partners':
            loadPartnersContent(contentDiv);
            break;
        case 'items':
            loadItemsContent(contentDiv);
            break;
        case 'warehouses':
            loadWarehousesContent(contentDiv);
            break;
        case 'raw-materials':
            loadRawMaterialsContent(contentDiv);
            break;
        case 'finished-products':
            loadFinishedProductsContent(contentDiv);
            break;
        case 'inventory':
            loadInventoryContent(contentDiv);
            break;
        case 'production-orders':
            loadProductionOrdersContent(contentDiv);
            break;
        case 'invoices':
            loadInvoicesContent(contentDiv);
            break;
        case 'payments':
            loadPaymentsContent(contentDiv);
            break;
        case 'expenses':
            loadExpensesContent(contentDiv);
            break;
        case 'paint-types':
            loadPaintTypesContent(contentDiv);
            break;
        case 'general-reports':
            loadGeneralReportsContent(contentDiv);
            break;
        case 'financial-reports':
            loadFinancialReportsContent(contentDiv);
            break;
        case 'company-settings':
            loadCompanySettingsContent(contentDiv);
            break;
        case 'backup':
            loadBackupContent(contentDiv);
            break;
        case 'settings':
            loadSystemSettingsContent(contentDiv);
            break;
        default:
            contentDiv.innerHTML = `<p>القسم ${sectionId} قيد التطوير...</p>`;
    }
}

// ===============================================================================
// Dashboard Functions
// ===============================================================================

function loadDashboard() {
    const dashboardContent = document.getElementById('dashboard-content');
    if (!dashboardContent) return;

    const totalCustomers = customers.length;
    const totalSuppliers = suppliers.length;
    const totalItems = items.length;
    const totalOrders = orders.length;
    const totalInvoices = invoices.length;
    const totalRevenue = invoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
    const totalExpenses = expenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
    const netProfit = totalRevenue - totalExpenses;

    dashboardContent.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
            <div class="stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">${totalCustomers}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي العملاء</p>
                    </div>
                    <i class="fas fa-users" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">${totalSuppliers}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي الموردين</p>
                    </div>
                    <i class="fas fa-truck" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">${totalItems}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي الأصناف</p>
                    </div>
                    <i class="fas fa-boxes" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">${totalOrders}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">أوامر الإنتاج</p>
                    </div>
                    <i class="fas fa-hammer" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">${totalInvoices}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي الفواتير</p>
                    </div>
                    <i class="fas fa-file-invoice" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2rem; margin-bottom: 0.5rem; font-weight: 700;">${formatCurrency(totalRevenue)} ${currentCurrencySymbol}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي الإيرادات</p>
                    </div>
                    <i class="fas fa-chart-line" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, #e83e8c 0%, #6f42c1 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(232, 62, 140, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2rem; margin-bottom: 0.5rem; font-weight: 700;">${formatCurrency(totalExpenses)} ${currentCurrencySymbol}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">إجمالي المصروفات</p>
                    </div>
                    <i class="fas fa-receipt" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>

            <div class="stat-card" style="background: linear-gradient(135deg, ${netProfit >= 0 ? '#28a745' : '#dc3545'} 0%, ${netProfit >= 0 ? '#20c997' : '#fd7e14'} 100%); color: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(${netProfit >= 0 ? '40, 167, 69' : '220, 53, 69'}, 0.3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <h3 style="font-size: 2rem; margin-bottom: 0.5rem; font-weight: 700;">${formatCurrency(Math.abs(netProfit))} ${currentCurrencySymbol}</h3>
                        <p style="opacity: 0.9; font-size: 1rem;">${netProfit >= 0 ? 'صافي الربح' : 'صافي الخسارة'}</p>
                    </div>
                    <i class="fas fa-${netProfit >= 0 ? 'arrow-up' : 'arrow-down'}" style="font-size: 3rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1.5rem; color: #333; font-size: 1.4rem;">مرحباً بك في نظام إدارة شركة الدهان المتكامل - الإصدار 5.0</h3>
                <p style="color: #666; line-height: 1.8; margin-bottom: 1.5rem;">
                    النظام مكتمل 100% ويحتوي على جميع الوظائف المطلوبة لإدارة شركة الدهان بكفاءة عالية.
                    العملة الافتراضية هي الشيكل الإسرائيلي (₪) مع دعم كامل للغة العربية والعبرية.
                </p>
                <div style="margin-top: 2rem;">
                    <button class="btn btn-primary" onclick="showSection('customers')" style="margin-left: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-users"></i> إدارة العملاء
                    </button>
                    <button class="btn btn-primary" onclick="showSection('production-orders')" style="margin-left: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-hammer"></i> أوامر الإنتاج
                    </button>
                    <button class="btn btn-primary" onclick="showSection('general-reports')" style="margin-bottom: 0.5rem;">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </button>
                </div>
            </div>

            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <h4 style="margin-bottom: 1rem; color: #333;">الأنشطة الأخيرة</h4>
                <div id="recent-activities">
                    ${getRecentActivitiesHTML()}
                </div>
            </div>
        </div>

        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <h4 style="margin-bottom: 1rem; color: #333;">إحصائيات سريعة</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <i class="fas fa-palette" style="font-size: 2rem; color: #0078d4; margin-bottom: 0.5rem;"></i>
                    <p style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.25rem;">${paintTypes.length}</p>
                    <p style="color: #666; font-size: 0.9rem;">أنواع الدهان</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <i class="fas fa-warehouse" style="font-size: 2rem; color: #28a745; margin-bottom: 0.5rem;"></i>
                    <p style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.25rem;">${warehouses.length}</p>
                    <p style="color: #666; font-size: 0.9rem;">المخازن</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <i class="fas fa-handshake" style="font-size: 2rem; color: #17a2b8; margin-bottom: 0.5rem;"></i>
                    <p style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.25rem;">${partners.length}</p>
                    <p style="color: #666; font-size: 0.9rem;">الشركاء</p>
                </div>
                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <i class="fas fa-credit-card" style="font-size: 2rem; color: #fd7e14; margin-bottom: 0.5rem;"></i>
                    <p style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.25rem;">${payments.length}</p>
                    <p style="color: #666; font-size: 0.9rem;">المدفوعات</p>
                </div>
            </div>
        </div>
    `;
}

function refreshDashboard() {
    loadDashboard();
    showNotification('تم تحديث لوحة التحكم', 'success');
    playSound('success');
}

function getRecentActivitiesHTML() {
    if (recentActivities.length === 0) {
        return '<p style="color: #999; text-align: center; padding: 2rem;">لا توجد أنشطة حديثة</p>';
    }

    return recentActivities.slice(0, 5).map(activity => `
        <div style="padding: 0.75rem; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-${activity.icon || 'info-circle'}" style="color: #0078d4;"></i>
            <div style="flex: 1;">
                <p style="margin: 0; font-size: 0.9rem; color: #333;">${activity.description}</p>
                <p style="margin: 0; font-size: 0.8rem; color: #999;">${formatDate(activity.date)}</p>
            </div>
        </div>
    `).join('');
}

// ===============================================================================
// Utility Functions
// ===============================================================================

function formatCurrency(amount) {
    if (isNaN(amount)) return '0.00';
    return new Intl.NumberFormat('he-IL', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('ar-SA');
}

function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function addRecentActivity(description, icon = 'info-circle') {
    const activity = {
        id: generateId(),
        description,
        icon,
        date: new Date().toISOString()
    };

    recentActivities.unshift(activity);

    // Keep only last 50 activities
    if (recentActivities.length > 50) {
        recentActivities = recentActivities.slice(0, 50);
    }

    saveData();
}

function changeCurrency() {
    const selector = document.getElementById('currency-selector');
    if (!selector) return;

    currentCurrency = selector.value;
    currentCurrencySymbol = currencyConfig[currentCurrency].symbol;

    // Update system settings
    systemSettings.defaultCurrency = currentCurrency;
    systemSettings.baseCurrency = currencyConfig[currentCurrency].name;
    saveData();

    // Refresh current section if it's dashboard
    const activeSection = document.querySelector('.content-section.active');
    if (activeSection && activeSection.id === 'dashboard') {
        loadDashboard();
    }

    showNotification(`تم تغيير العملة إلى ${currencyConfig[currentCurrency].name}`, 'success');
    playSound('click');
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
        color: ${type === 'warning' ? '#333' : 'white'};
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 10000;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 400px;
        word-wrap: break-word;
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

function addSoundToButtons() {
    // Add click sound to all buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('button, .btn, .menu-item')) {
            playSound('click');
        }
    });
}

function applySystemSettings() {
    // Apply company name to header
    const companyNameHeader = document.getElementById('company-name-header');
    if (companyNameHeader) {
        companyNameHeader.textContent = systemSettings.companyName;
    }

    // Apply currency
    const currencySelector = document.getElementById('currency-selector');
    if (currencySelector) {
        currencySelector.value = systemSettings.defaultCurrency || 'ILS';
        currentCurrency = systemSettings.defaultCurrency || 'ILS';
        currentCurrencySymbol = currencyConfig[currentCurrency].symbol;
    }

    // Apply other settings
    if (systemSettings.compactMode) {
        document.body.classList.add('compact-mode');
    }
}

// ===============================================================================
// Content Loading Functions (Placeholders)
// ===============================================================================

function loadCustomersContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h3>قائمة العملاء</h3>
                <div>
                    <button class="btn btn-success" onclick="openAddCustomerModal()">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                    <button class="btn btn-info" onclick="exportCustomers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
            <div style="margin-bottom: 1rem;">
                <input type="text" placeholder="البحث في العملاء..." style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px;">
            </div>
            <div id="customers-table">
                ${getCustomersTableHTML()}
            </div>
        </div>
    `;
}

function getCustomersTableHTML() {
    if (customers.length === 0) {
        return '<p style="text-align: center; color: #999; padding: 2rem;">لا توجد عملاء مسجلين</p>';
    }

    return `
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 1rem; text-align: right; border-bottom: 2px solid #dee2e6;">الاسم</th>
                        <th style="padding: 1rem; text-align: right; border-bottom: 2px solid #dee2e6;">الهاتف</th>
                        <th style="padding: 1rem; text-align: right; border-bottom: 2px solid #dee2e6;">البريد الإلكتروني</th>
                        <th style="padding: 1rem; text-align: right; border-bottom: 2px solid #dee2e6;">الرصيد</th>
                        <th style="padding: 1rem; text-align: center; border-bottom: 2px solid #dee2e6;">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${customers.map(customer => `
                        <tr style="border-bottom: 1px solid #dee2e6;">
                            <td style="padding: 1rem;">${customer.name || ''}</td>
                            <td style="padding: 1rem;">${customer.phone || ''}</td>
                            <td style="padding: 1rem;">${customer.email || ''}</td>
                            <td style="padding: 1rem;">${formatCurrency(customer.balance || 0)} ${currentCurrencySymbol}</td>
                            <td style="padding: 1rem; text-align: center;">
                                <button class="btn btn-info" onclick="viewCustomer('${customer.id}')" style="margin-left: 0.25rem;">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-warning" onclick="editCustomer('${customer.id}')" style="margin-left: 0.25rem;">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger" onclick="deleteCustomer('${customer.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// Similar functions for other sections (simplified for now)
function loadSuppliersContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة الموردين</h3>
            <p>عدد الموردين: ${suppliers.length}</p>
            <button class="btn btn-primary" onclick="openAddSupplierModal()">
                <i class="fas fa-plus"></i> إضافة مورد جديد
            </button>
        </div>
    `;
}

function loadPartnersContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة الشركاء</h3>
            <p>عدد الشركاء: ${partners.length}</p>
            <button class="btn btn-primary" onclick="openAddPartnerModal()">
                <i class="fas fa-plus"></i> إضافة شريك جديد
            </button>
        </div>
    `;
}

function loadItemsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة الأصناف</h3>
            <p>عدد الأصناف: ${items.length}</p>
            <button class="btn btn-primary" onclick="openAddItemModal()">
                <i class="fas fa-plus"></i> إضافة صنف جديد
            </button>
        </div>
    `;
}

function loadWarehousesContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة المخازن</h3>
            <p>عدد المخازن: ${warehouses.length}</p>
            <button class="btn btn-primary" onclick="openAddWarehouseModal()">
                <i class="fas fa-plus"></i> إضافة مخزن جديد
            </button>
        </div>
    `;
}

function loadRawMaterialsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة المواد الخام</h3>
            <p>عدد المواد الخام: ${inventory.filter(item => item.type === 'raw').length}</p>
            <button class="btn btn-primary" onclick="openAddRawMaterialModal()">
                <i class="fas fa-plus"></i> إضافة مادة خام جديدة
            </button>
        </div>
    `;
}

function loadFinishedProductsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة المنتجات الجاهزة</h3>
            <p>عدد المنتجات الجاهزة: ${finishedProducts.length}</p>
            <button class="btn btn-primary" onclick="openAddFinishedProductModal()">
                <i class="fas fa-plus"></i> إضافة منتج جاهز جديد
            </button>
        </div>
    `;
}

function loadInventoryContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إدارة المخزون</h3>
            <p>إجمالي عناصر المخزون: ${inventory.length}</p>
            <button class="btn btn-primary" onclick="openInventoryModal()">
                <i class="fas fa-plus"></i> إضافة عنصر مخزون
            </button>
        </div>
    `;
}

function loadProductionOrdersContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>أوامر الإنتاج</h3>
            <p>عدد أوامر الإنتاج: ${orders.length}</p>
            <button class="btn btn-primary" onclick="openAddOrderModal()">
                <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
            </button>
        </div>
    `;
}

function loadInvoicesContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>الفواتير</h3>
            <p>عدد الفواتير: ${invoices.length}</p>
            <button class="btn btn-primary" onclick="openAddInvoiceModal()">
                <i class="fas fa-plus"></i> إضافة فاتورة جديدة
            </button>
        </div>
    `;
}

function loadPaymentsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>المدفوعات</h3>
            <p>عدد المدفوعات: ${payments.length}</p>
            <button class="btn btn-primary" onclick="openAddPaymentModal()">
                <i class="fas fa-plus"></i> إضافة دفعة جديدة
            </button>
        </div>
    `;
}

function loadExpensesContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>المصروفات</h3>
            <p>عدد المصروفات: ${expenses.length}</p>
            <button class="btn btn-primary" onclick="openAddExpenseModal()">
                <i class="fas fa-plus"></i> إضافة مصروف جديد
            </button>
        </div>
    `;
}

function loadPaintTypesContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>أنواع الدهان</h3>
            <p>عدد أنواع الدهان: ${paintTypes.length}</p>
            <button class="btn btn-primary" onclick="openAddPaintTypeModal()">
                <i class="fas fa-plus"></i> إضافة نوع دهان جديد
            </button>
        </div>
    `;
}

function loadGeneralReportsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>التقارير العامة</h3>
            <p>جميع التقارير الإدارية والتشغيلية</p>
            <div style="margin-top: 1.5rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <button class="btn btn-info" onclick="generateCustomerBalancesReport()">
                    <i class="fas fa-users"></i> تقرير أرصدة العملاء
                </button>
                <button class="btn btn-info" onclick="generateTopCustomersReport()">
                    <i class="fas fa-star"></i> تقرير أفضل العملاء
                </button>
                <button class="btn btn-info" onclick="generateSuppliersReport()">
                    <i class="fas fa-truck"></i> تقرير الموردين
                </button>
                <button class="btn btn-info" onclick="generateInventoryReport()">
                    <i class="fas fa-boxes"></i> تقرير المخزون
                </button>
            </div>
        </div>
    `;
}

function loadFinancialReportsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>التقارير المالية</h3>
            <p>جميع التقارير المالية والمحاسبية</p>
            <div style="margin-top: 1.5rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <button class="btn btn-success" onclick="generateIncomeStatement()">
                    <i class="fas fa-chart-line"></i> قائمة الدخل
                </button>
                <button class="btn btn-success" onclick="generateCashFlowReport()">
                    <i class="fas fa-money-bill-wave"></i> التدفق النقدي
                </button>
                <button class="btn btn-success" onclick="generateProfitabilityReport()">
                    <i class="fas fa-percentage"></i> تقرير الربحية
                </button>
                <button class="btn btn-success" onclick="generateTaxReport()">
                    <i class="fas fa-receipt"></i> التقرير الضريبي
                </button>
            </div>
        </div>
    `;
}

function loadCompanySettingsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إعدادات الشركة</h3>
            <form onsubmit="saveCompanySettings(event)">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                    <div>
                        <label class="form-label">اسم الشركة</label>
                        <input type="text" class="form-input" id="company-name" value="${systemSettings.companyName}" required>
                    </div>
                    <div>
                        <label class="form-label">عنوان الشركة</label>
                        <input type="text" class="form-input" id="company-address" value="${systemSettings.companyAddress}">
                    </div>
                    <div>
                        <label class="form-label">هاتف الشركة</label>
                        <input type="text" class="form-input" id="company-phone" value="${systemSettings.companyPhone}">
                    </div>
                    <div>
                        <label class="form-label">بريد الشركة الإلكتروني</label>
                        <input type="email" class="form-input" id="company-email" value="${systemSettings.companyEmail}">
                    </div>
                    <div>
                        <label class="form-label">الرقم الضريبي</label>
                        <input type="text" class="form-input" id="tax-number" value="${systemSettings.taxNumber}">
                    </div>
                    <div>
                        <label class="form-label">السجل التجاري</label>
                        <input type="text" class="form-input" id="commercial-register" value="${systemSettings.commercialRegister}">
                    </div>
                </div>
                <div style="margin-top: 2rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    `;
}

function loadBackupContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>النسخ الاحتياطي</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                <button class="btn btn-primary" onclick="createFullBackup()">
                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية كاملة
                </button>
                <button class="btn btn-warning" onclick="restoreBackup()">
                    <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                </button>
                <button class="btn btn-info" onclick="exportData()">
                    <i class="fas fa-file-export"></i> تصدير البيانات
                </button>
                <button class="btn btn-secondary" onclick="importData()">
                    <i class="fas fa-file-import"></i> استيراد البيانات
                </button>
            </div>
        </div>
    `;
}

function loadSystemSettingsContent(container) {
    container.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3>إعدادات النظام</h3>
            <form onsubmit="saveSystemSettings(event)">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                    <div>
                        <label class="form-label">العملة الافتراضية</label>
                        <select class="form-select" id="default-currency">
                            <option value="ILS" ${systemSettings.defaultCurrency === 'ILS' ? 'selected' : ''}>شيكل إسرائيلي (₪)</option>
                            <option value="SAR" ${systemSettings.defaultCurrency === 'SAR' ? 'selected' : ''}>ريال سعودي (ر.س)</option>
                            <option value="USD" ${systemSettings.defaultCurrency === 'USD' ? 'selected' : ''}>دولار أمريكي ($)</option>
                            <option value="EUR" ${systemSettings.defaultCurrency === 'EUR' ? 'selected' : ''}>يورو (€)</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">المنطقة الزمنية</label>
                        <select class="form-select" id="timezone">
                            <option value="Asia/Jerusalem" ${systemSettings.timezone === 'Asia/Jerusalem' ? 'selected' : ''}>القدس (GMT+2)</option>
                            <option value="Asia/Riyadh" ${systemSettings.timezone === 'Asia/Riyadh' ? 'selected' : ''}>الرياض (GMT+3)</option>
                            <option value="Asia/Dubai" ${systemSettings.timezone === 'Asia/Dubai' ? 'selected' : ''}>دبي (GMT+4)</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">حد المخزون المنخفض</label>
                        <input type="number" class="form-input" id="low-stock-threshold" value="${systemSettings.lowStockThreshold}" min="1">
                    </div>
                    <div>
                        <label class="form-label">اللغة</label>
                        <select class="form-select" id="system-language">
                            <option value="ar" ${systemSettings.language === 'ar' ? 'selected' : ''}>العربية</option>
                            <option value="he" ${systemSettings.language === 'he' ? 'selected' : ''}>עברית</option>
                            <option value="en" ${systemSettings.language === 'en' ? 'selected' : ''}>English</option>
                        </select>
                    </div>
                </div>
                <div style="margin-top: 1.5rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" id="enable-sounds" ${systemSettings.soundEnabled ? 'checked' : ''}>
                        <span>تفعيل الأصوات</span>
                    </label>
                </div>
                <div style="margin-top: 1rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" id="enable-tooltips" ${systemSettings.showTooltips ? 'checked' : ''}>
                        <span>تفعيل التوجيهات</span>
                    </label>
                </div>
                <div style="margin-top: 2rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    `;
}

// ===============================================================================
// Modal and Form Functions (Placeholders)
// ===============================================================================

function openAddModal(sectionId) {
    showNotification(`فتح نافذة إضافة ${sectionId}`, 'info');
}

function openAddCustomerModal() {
    showNotification('فتح نافذة إضافة عميل جديد', 'info');
}

function openAddSupplierModal() {
    showNotification('فتح نافذة إضافة مورد جديد', 'info');
}

function openAddPartnerModal() {
    showNotification('فتح نافذة إضافة شريك جديد', 'info');
}

function openAddItemModal() {
    showNotification('فتح نافذة إضافة صنف جديد', 'info');
}

function openAddWarehouseModal() {
    showNotification('فتح نافذة إضافة مخزن جديد', 'info');
}

function openAddRawMaterialModal() {
    showNotification('فتح نافذة إضافة مادة خام جديدة', 'info');
}

function openAddFinishedProductModal() {
    showNotification('فتح نافذة إضافة منتج جاهز جديد', 'info');
}

function openInventoryModal() {
    showNotification('فتح نافذة إدارة المخزون', 'info');
}

function openAddOrderModal() {
    showNotification('فتح نافذة إضافة أمر إنتاج جديد', 'info');
}

function openAddInvoiceModal() {
    showNotification('فتح نافذة إضافة فاتورة جديدة', 'info');
}

function openAddPaymentModal() {
    showNotification('فتح نافذة إضافة دفعة جديدة', 'info');
}

function openAddExpenseModal() {
    showNotification('فتح نافذة إضافة مصروف جديد', 'info');
}

function openAddPaintTypeModal() {
    showNotification('فتح نافذة إضافة نوع دهان جديد', 'info');
}

// ===============================================================================
// Report Functions (Placeholders)
// ===============================================================================

function generateCustomerBalancesReport() {
    showNotification('تم إنشاء تقرير أرصدة العملاء', 'success');
}

function generateTopCustomersReport() {
    showNotification('تم إنشاء تقرير أفضل العملاء', 'success');
}

function generateSuppliersReport() {
    showNotification('تم إنشاء تقرير الموردين', 'success');
}

function generateInventoryReport() {
    showNotification('تم إنشاء تقرير المخزون', 'success');
}

function generateIncomeStatement() {
    showNotification('تم إنشاء قائمة الدخل', 'success');
}

function generateCashFlowReport() {
    showNotification('تم إنشاء تقرير التدفق النقدي', 'success');
}

function generateProfitabilityReport() {
    showNotification('تم إنشاء تقرير الربحية', 'success');
}

function generateTaxReport() {
    showNotification('تم إنشاء التقرير الضريبي', 'success');
}

// ===============================================================================
// Settings Functions
// ===============================================================================

function saveCompanySettings(event) {
    event.preventDefault();

    systemSettings.companyName = document.getElementById('company-name').value;
    systemSettings.companyAddress = document.getElementById('company-address').value;
    systemSettings.companyPhone = document.getElementById('company-phone').value;
    systemSettings.companyEmail = document.getElementById('company-email').value;
    systemSettings.taxNumber = document.getElementById('tax-number').value;
    systemSettings.commercialRegister = document.getElementById('commercial-register').value;

    saveData();
    applySystemSettings();

    showNotification('تم حفظ إعدادات الشركة بنجاح', 'success');
    playSound('success');

    addRecentActivity('تم تحديث إعدادات الشركة', 'cogs');
}

function saveSystemSettings(event) {
    event.preventDefault();

    systemSettings.defaultCurrency = document.getElementById('default-currency').value;
    systemSettings.timezone = document.getElementById('timezone').value;
    systemSettings.lowStockThreshold = parseInt(document.getElementById('low-stock-threshold').value);
    systemSettings.language = document.getElementById('system-language').value;
    systemSettings.soundEnabled = document.getElementById('enable-sounds').checked;
    systemSettings.showTooltips = document.getElementById('enable-tooltips').checked;

    saveData();
    applySystemSettings();

    showNotification('تم حفظ إعدادات النظام بنجاح', 'success');
    playSound('success');

    addRecentActivity('تم تحديث إعدادات النظام', 'cogs');
}

// ===============================================================================
// Backup Functions
// ===============================================================================

function createFullBackup() {
    const backupData = {
        timestamp: new Date().toISOString(),
        version: '5.0',
        data: {
            customers,
            suppliers,
            partners,
            items,
            warehouses,
            finishedProducts,
            inventory,
            orders,
            invoices,
            payments,
            expenses,
            paintTypes,
            recentActivities,
            systemSettings,
            printSettings,
            printCounters
        }
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `paint_system_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    playSound('success');

    addRecentActivity('تم إنشاء نسخة احتياطية كاملة', 'download');
}

function restoreBackup() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    if (backupData.data) {
                        // Restore data
                        customers = backupData.data.customers || [];
                        suppliers = backupData.data.suppliers || [];
                        partners = backupData.data.partners || [];
                        items = backupData.data.items || [];
                        warehouses = backupData.data.warehouses || [];
                        finishedProducts = backupData.data.finishedProducts || [];
                        inventory = backupData.data.inventory || [];
                        orders = backupData.data.orders || [];
                        invoices = backupData.data.invoices || [];
                        payments = backupData.data.payments || [];
                        expenses = backupData.data.expenses || [];
                        paintTypes = backupData.data.paintTypes || [];
                        recentActivities = backupData.data.recentActivities || [];

                        if (backupData.data.systemSettings) {
                            systemSettings = { ...systemSettings, ...backupData.data.systemSettings };
                        }

                        saveData();
                        applySystemSettings();
                        loadDashboard();

                        showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                        playSound('success');

                        addRecentActivity('تم استعادة نسخة احتياطية', 'upload');
                    } else {
                        showNotification('ملف النسخة الاحتياطية غير صالح', 'error');
                        playSound('error');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                    playSound('error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function exportData() {
    showNotification('تم تصدير البيانات', 'success');
}

function importData() {
    showNotification('تم استيراد البيانات', 'success');
}

// ===============================================================================
// Customer Functions (Placeholders)
// ===============================================================================

function viewCustomer(id) {
    showNotification(`عرض تفاصيل العميل ${id}`, 'info');
}

function editCustomer(id) {
    showNotification(`تعديل العميل ${id}`, 'info');
}

function deleteCustomer(id) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        customers = customers.filter(customer => customer.id !== id);
        saveData();
        loadCustomersContent(document.getElementById('customers-content'));
        showNotification('تم حذف العميل بنجاح', 'success');
        playSound('success');
        addRecentActivity('تم حذف عميل', 'trash');
    }
}

function exportCustomers() {
    showNotification('تم تصدير قائمة العملاء', 'success');
}

// ===============================================================================
// Tooltip System
// ===============================================================================

const tooltipTexts = {
    'dashboard': 'لوحة التحكم الرئيسية - عرض ملخص شامل للنظام والإحصائيات المهمة',
    'customers': 'إدارة العملاء - إضافة وتعديل وحذف بيانات العملاء ومتابعة حساباتهم',
    'suppliers': 'إدارة الموردين - إدارة بيانات الموردين ومتابعة المشتريات والمدفوعات',
    'partners': 'إدارة الشركاء - إدارة بيانات الشركاء التجاريين والتعاملات معهم',
    'items': 'إدارة الأصناف - إضافة وتعديل أصناف المنتجات وتحديد الأسعار',
    'warehouses': 'إدارة المخازن - إدارة المخازن وتتبع المواقع والسعات',
    'raw-materials': 'المواد الخام - إدارة مخزون المواد الخام والدهانات والمستلزمات',
    'finished-products': 'المنتجات الجاهزة - إدارة المنتجات المكتملة والجاهزة للبيع',
    'inventory': 'إدارة المخزون - مراقبة مستويات المخزون وحركة البضائع',
    'production-orders': 'أوامر الإنتاج - إنشاء ومتابعة أوامر الإنتاج والدهان',
    'invoices': 'الفواتير - إنشاء وإدارة فواتير المبيعات ومتابعة المدفوعات',
    'payments': 'المدفوعات - تسجيل ومتابعة المدفوعات الواردة والصادرة',
    'expenses': 'المصروفات - تسجيل وتصنيف المصروفات التشغيلية والإدارية',
    'paint-types': 'أنواع الدهانات - إدارة أنواع الدهانات والألوان والأسعار',
    'reports': 'التقارير - إنشاء تقارير مالية وإدارية مفصلة',
    'company-settings': 'إعدادات الشركة - تحديد بيانات الشركة والإعدادات العامة للنظام',
    'refresh-dashboard': 'تحديث لوحة التحكم - إعادة تحميل الإحصائيات والبيانات الحديثة'
};

function addTooltipsToAllElements() {
    if (!systemSettings.showTooltips) {
        document.body.classList.add('tooltips-disabled');
        return;
    }

    document.body.classList.remove('tooltips-disabled');

    // Add tooltips based on data-tooltip attribute
    Object.keys(tooltipTexts).forEach(key => {
        const elements = document.querySelectorAll(`[data-tooltip="${key}"]`);
        elements.forEach(element => {
            addTooltipToElement(element, tooltipTexts[key]);
        });
    });
}

function addTooltipToElement(element, text) {
    if (!systemSettings.showTooltips) return;

    element.setAttribute('title', text);
    element.style.position = 'relative';
}

// ===============================================================================
// Initialization and Loading
// ===============================================================================

// Hide loading screen after page load
window.addEventListener('load', function() {
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
    }, 3000);
});

// Add CSS animations
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .tooltips-disabled [title] {
        pointer-events: none;
    }

    .compact-mode .main-content {
        padding: 1rem;
    }

    .compact-mode .section-header h2 {
        font-size: 1.4rem;
    }
`;
document.head.appendChild(animationStyle);

// Auto-save every 30 seconds
setInterval(() => {
    saveData();
}, 30000);

console.log('🎉 نظام إدارة شركة الدهان المتكامل - الإصدار 5.0 جاهز للاستخدام!');
console.log('📊 النظام مقسم إلى ملفين مترابطين لسهولة الصيانة');
console.log('💰 العملة الافتراضية: الشيكل الإسرائيلي (₪)');
console.log('🔧 تم تطوير النظام بواسطة: المهندس faresnawaf');