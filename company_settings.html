<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الشركة - نظام محاسبة الدهان</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: var(--background-top, 50%);
            left: var(--background-left, 50%);
            right: var(--background-right, auto);
            bottom: var(--background-bottom, auto);
            transform: var(--background-transform, translate(-50%, -50%));
            width: var(--background-size, 400px);
            height: var(--background-size, 400px);
            background-image: var(--company-logo, none);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: var(--background-opacity, 0.08);
            z-index: 0;
            pointer-events: none;
            transition: all 0.5s ease;
            animation: var(--background-animation, floatLogo 6s ease-in-out infinite);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .header {
            background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #0078d4;
        }

        .section h2 {
            color: #0078d4;
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-input, .form-select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #0078d4;
            box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            border: 2px dashed #0078d4;
            border-radius: 8px;
            background: #f0f8ff;
            color: #0078d4;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .file-upload:hover .file-upload-label {
            background: #e6f3ff;
            border-color: #005a9e;
        }

        .logo-preview {
            text-align: center;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            margin-top: 15px;
            background: white;
        }

        .logo-preview img {
            max-width: 200px;
            max-height: 100px;
            object-fit: contain;
            border-radius: 8px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #0078d4;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            margin: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 120, 212, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }

        .actions {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .notification.error {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }

        .notification.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #0078d4;
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-link:hover {
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: #e6f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0056b3;
        }

        .info-box i {
            margin-left: 8px;
        }

        /* تأثيرات الخلفية */
        @keyframes floatLogo {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.05);
            }
        }

        body::before {
            animation: floatLogo 6s ease-in-out infinite;
        }

        /* تأثير عند تحميل الصفحة */
        @keyframes fadeInLogo {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 0.08;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        body.loaded::before {
            animation: fadeInLogo 1s ease-out, floatLogo 6s ease-in-out infinite 1s;
        }
    </style>
</head>
<body>
    <a href="paint_system_complete.html" class="back-link">
        <i class="fas fa-arrow-right"></i>
        العودة للنظام الرئيسي
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> إعدادات الشركة</h1>
            <p>قم بتخصيص معلومات شركتك وإعدادات الطباعة</p>
        </div>

        <div class="content">
            <!-- معلومات الشركة -->
            <div class="section">
                <h2><i class="fas fa-building"></i> معلومات الشركة</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    هذه المعلومات ستظهر في جميع المستندات والتقارير المطبوعة بتنسيق أفقي مضغوط
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">اسم الشركة *</label>
                        <input type="text" id="company-name" class="form-input" placeholder="أدخل اسم الشركة">
                    </div>
                    <div class="form-group">
                        <label class="form-label">العنوان *</label>
                        <input type="text" id="company-address" class="form-input" placeholder="أدخل عنوان الشركة">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف *</label>
                        <input type="tel" id="company-phone" class="form-input" placeholder="05xxxxxxxx">
                    </div>
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="company-email" class="form-input" placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">الموقع الإلكتروني</label>
                        <input type="url" id="company-website" class="form-input" placeholder="www.company.com">
                    </div>
                    <div class="form-group">
                        <label class="form-label">السجل التجاري</label>
                        <input type="text" id="company-cr" class="form-input" placeholder="1010xxxxxx">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الرقم الضريبي</label>
                    <input type="text" id="company-tax" class="form-input" placeholder="3001234567890xx">
                </div>
            </div>

            <!-- إعدادات اللوجو -->
            <div class="section">
                <h2><i class="fas fa-image"></i> لوجو الشركة</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    يُفضل أن يكون اللوجو بصيغة PNG أو JPG وبحجم لا يزيد عن 2MB
                </div>
                
                <div class="form-group">
                    <label class="form-label">رفع لوجو الشركة</label>
                    <div class="file-upload">
                        <input type="file" id="company-logo" accept="image/*">
                        <div class="file-upload-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            اضغط هنا لرفع اللوجو أو اسحب الملف
                        </div>
                    </div>
                </div>
                
                <div id="logo-preview" class="logo-preview" style="display: none;">
                    <!-- سيتم عرض اللوجو هنا -->
                </div>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label class="form-label">حجم اللوجو في الطباعة</label>
                    <select id="logo-size" class="form-select">
                        <option value="small">صغير (50px)</option>
                        <option value="medium" selected>متوسط (80px)</option>
                        <option value="large">كبير (120px)</option>
                    </select>
                </div>
            </div>

            <!-- إعدادات الطباعة -->
            <div class="section">
                <h2><i class="fas fa-print"></i> إعدادات الطباعة</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    اختر المعلومات التي تريد إظهارها في المستندات المطبوعة
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="show-logo-print" checked>
                    <label for="show-logo-print" class="form-label">عرض اللوجو في المستندات المطبوعة</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="show-company-info" checked>
                    <label for="show-company-info" class="form-label">عرض معلومات الشركة في المستندات</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="show-developer-info" checked>
                    <label for="show-developer-info" class="form-label">عرض معلومات المطور في المستندات</label>
                </div>
            </div>

            <!-- إعدادات خلفية الصفحة الرئيسية -->
            <div class="section">
                <h2><i class="fas fa-desktop"></i> إعدادات خلفية الصفحة الرئيسية</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    تحكم في عرض لوجو الشركة في خلفية الصفحة الرئيسية (لوحة التحكم)
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="show-background-logo" checked>
                    <label for="show-background-logo" class="form-label">عرض لوجو الشركة في خلفية الصفحة الرئيسية</label>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">شفافية اللوجو في الخلفية</label>
                        <select id="background-opacity" class="form-select">
                            <option value="0.03">شفافية خفيفة جداً (3%)</option>
                            <option value="0.05">شفافية خفيفة (5%)</option>
                            <option value="0.08" selected>شفافية متوسطة (8%)</option>
                            <option value="0.12">شفافية عالية (12%)</option>
                            <option value="0.15">شفافية عالية جداً (15%)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">حجم اللوجو في الخلفية</label>
                        <select id="background-size" class="form-select">
                            <option value="300">صغير (300px)</option>
                            <option value="400" selected>متوسط (400px)</option>
                            <option value="500">كبير (500px)</option>
                            <option value="600">كبير جداً (600px)</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">موضع اللوجو في الخلفية</label>
                        <select id="background-position" class="form-select">
                            <option value="center" selected>وسط الشاشة</option>
                            <option value="top-right">أعلى اليمين</option>
                            <option value="top-left">أعلى اليسار</option>
                            <option value="bottom-right">أسفل اليمين</option>
                            <option value="bottom-left">أسفل اليسار</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="background-animation" checked>
                            <label for="background-animation" class="form-label">تفعيل الحركة الناعمة للوجو</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات المطور -->
            <div class="section">
                <h2><i class="fas fa-code"></i> معلومات المطور</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    هذه المعلومات ستظهر في أسفل المستندات المطبوعة (يمكن إخفاؤها من إعدادات الطباعة)
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">اسم المطور</label>
                        <input type="text" id="developer-name" class="form-input" placeholder="اسم المطور">
                    </div>
                    <div class="form-group">
                        <label class="form-label">رقم التواصل</label>
                        <input type="tel" id="developer-phone" class="form-input" placeholder="05xxxxxxxx">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">وصف النظام</label>
                    <input type="text" id="system-description" class="form-input" placeholder="وصف النظام">
                </div>
            </div>
        </div>

        <!-- معاينة التنسيق -->
        <div class="content">
            <div class="section">
                <h2><i class="fas fa-eye"></i> معاينة التنسيق الجديد</h2>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    هذا مثال على كيفية ظهور رأس وتذييل المستندات بالتنسيق الأفقي المضغوط. اللوجو يظهر أيضاً في خلفية الصفحة بشفافية.
                </div>

                <div id="preview-container" style="border: 2px dashed #ddd; padding: 20px; background: white; border-radius: 8px; font-family: Arial, sans-serif; font-size: 0.9rem;">
                    <!-- سيتم عرض المعاينة هنا -->
                </div>

                <button class="btn btn-secondary" onclick="updatePreview()" style="margin-top: 15px;">
                    <i class="fas fa-refresh"></i> تحديث المعاينة
                </button>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="saveSettings()">
                <i class="fas fa-save"></i> حفظ الإعدادات
            </button>
            <button class="btn btn-secondary" onclick="resetSettings()">
                <i class="fas fa-undo"></i> استعادة الإعدادات الافتراضية
            </button>
            <button class="btn btn-danger" onclick="removeLogo()">
                <i class="fas fa-trash"></i> إزالة اللوجو
            </button>
        </div>
    </div>

    <script>
        // إعدادات النظام الافتراضية
        let systemSettings = {
            companyName: 'شركة الدهان المتخصصة',
            companyAddress: 'القدس - فلسطين',
            companyPhone: '02-1234567',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.paintcompany.ps',
            companyCR: '1010123456',
            companyTax: '300123456789003',
            companyLogo: '',
            showLogoInPrint: true,
            showCompanyInfo: true,
            showDeveloperInfo: true,
            logoSize: 'medium',
            developerName: 'المهندس محمد الأحمد',
            developerPhone: '0501234567',
            systemDescription: 'نظام محاسبة شركات الدهان المتكامل',
            // إعدادات خلفية الشاشة
            showBackgroundLogo: true,
            backgroundOpacity: '0.08',
            backgroundSize: '400',
            backgroundAnimation: true,
            backgroundPosition: 'center'
        };

        // تحميل الإعدادات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            updatePreview();
            updateBackgroundLogo();

            // إضافة تأثير التحميل
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 100);
        });

        // تحميل الإعدادات من localStorage
        function loadSettings() {
            const savedSettings = localStorage.getItem('systemSettings');
            if (savedSettings) {
                systemSettings = { ...systemSettings, ...JSON.parse(savedSettings) };
            }

            // تحميل إعدادات الشركة
            document.getElementById('company-name').value = systemSettings.companyName;
            document.getElementById('company-address').value = systemSettings.companyAddress;
            document.getElementById('company-phone').value = systemSettings.companyPhone;
            document.getElementById('company-email').value = systemSettings.companyEmail;
            document.getElementById('company-website').value = systemSettings.companyWebsite;
            document.getElementById('company-cr').value = systemSettings.companyCR;
            document.getElementById('company-tax').value = systemSettings.companyTax;

            // تحميل إعدادات الطباعة
            document.getElementById('show-logo-print').checked = systemSettings.showLogoInPrint;
            document.getElementById('show-company-info').checked = systemSettings.showCompanyInfo;
            document.getElementById('show-developer-info').checked = systemSettings.showDeveloperInfo;
            document.getElementById('logo-size').value = systemSettings.logoSize;

            // تحميل معلومات المطور
            document.getElementById('developer-name').value = systemSettings.developerName;
            document.getElementById('developer-phone').value = systemSettings.developerPhone;
            document.getElementById('system-description').value = systemSettings.systemDescription;

            // تحميل إعدادات خلفية الشاشة
            document.getElementById('show-background-logo').checked = systemSettings.showBackgroundLogo;
            document.getElementById('background-opacity').value = systemSettings.backgroundOpacity;
            document.getElementById('background-size').value = systemSettings.backgroundSize;
            document.getElementById('background-animation').checked = systemSettings.backgroundAnimation;
            document.getElementById('background-position').value = systemSettings.backgroundPosition;

            // عرض اللوجو إذا كان موجوداً
            if (systemSettings.companyLogo) {
                displayLogo(systemSettings.companyLogo);
            }

            // إضافة مستمعات للتحديث التلقائي للمعاينة
            const inputs = ['company-name', 'company-address', 'company-phone', 'company-email',
                           'company-website', 'company-cr', 'company-tax', 'developer-name',
                           'developer-phone', 'system-description'];

            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });

            const checkboxes = ['show-logo-print', 'show-company-info', 'show-developer-info',
                               'show-background-logo', 'background-animation'];
            checkboxes.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updatePreview);
                    if (id.startsWith('background-')) {
                        element.addEventListener('change', updateBackgroundLogo);
                    }
                }
            });

            const selects = ['logo-size', 'background-opacity', 'background-size', 'background-position'];
            selects.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updatePreview);
                    if (id.startsWith('background-')) {
                        element.addEventListener('change', updateBackgroundLogo);
                    }
                }
            });
        }

        // رفع اللوجو
        document.getElementById('company-logo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // فحص نوع الملف
            if (!file.type.startsWith('image/')) {
                showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            // فحص حجم الملف (2MB)
            if (file.size > 2 * 1024 * 1024) {
                showNotification('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 2MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const logoData = e.target.result;
                systemSettings.companyLogo = logoData;
                displayLogo(logoData);
                updatePreview(); // تحديث المعاينة
                updateBackgroundLogo(); // تحديث خلفية اللوجو
                showNotification('تم رفع اللوجو بنجاح', 'success');
            };
            reader.readAsDataURL(file);
        });

        // عرض اللوجو
        function displayLogo(logoData) {
            const preview = document.getElementById('logo-preview');
            preview.style.display = 'block';
            preview.innerHTML = `
                <img src="${logoData}" alt="لوجو الشركة">
                <p style="margin-top: 10px; color: #666; font-size: 0.9rem;">لوجو الشركة الحالي</p>
            `;
            updateBackgroundLogo();
        }

        // تحديث خلفية الصفحة الرئيسية
        function updateBackgroundLogo() {
            console.log('تحديث خلفية الصفحة الرئيسية...');

            // الحصول على الإعدادات الحالية
            const showBackground = document.getElementById('show-background-logo')?.checked ?? systemSettings.showBackgroundLogo;
            const opacity = document.getElementById('background-opacity')?.value ?? systemSettings.backgroundOpacity;
            const size = document.getElementById('background-size')?.value ?? systemSettings.backgroundSize;
            const animation = document.getElementById('background-animation')?.checked ?? systemSettings.backgroundAnimation;
            const position = document.getElementById('background-position')?.value ?? systemSettings.backgroundPosition;

            // حفظ الإعدادات في systemSettings
            systemSettings.showBackgroundLogo = showBackground;
            systemSettings.backgroundOpacity = opacity;
            systemSettings.backgroundSize = size;
            systemSettings.backgroundAnimation = animation;
            systemSettings.backgroundPosition = position;

            // إشعار النافذة الرئيسية بتحديث اللوجو
            if (window.opener && window.opener.updateMainPageLogo) {
                window.opener.updateMainPageLogo();
            }

            console.log('تم تحديث إعدادات خلفية الصفحة الرئيسية');
        }

        // إزالة اللوجو
        function removeLogo() {
            if (confirm('هل أنت متأكد من إزالة اللوجو؟')) {
                systemSettings.companyLogo = '';
                document.getElementById('logo-preview').style.display = 'none';
                document.getElementById('company-logo').value = '';
                updatePreview(); // تحديث المعاينة
                updateBackgroundLogo(); // تحديث خلفية الصفحة الرئيسية
                showNotification('تم إزالة اللوجو', 'info');
            }
        }

        // حفظ الإعدادات
        function saveSettings() {
            // التحقق من الحقول المطلوبة
            const requiredFields = [
                { id: 'company-name', name: 'اسم الشركة' },
                { id: 'company-address', name: 'العنوان' },
                { id: 'company-phone', name: 'رقم الهاتف' }
            ];

            for (let field of requiredFields) {
                const value = document.getElementById(field.id).value.trim();
                if (!value) {
                    showNotification(`يرجى إدخال ${field.name}`, 'error');
                    document.getElementById(field.id).focus();
                    return;
                }
            }

            // حفظ إعدادات الشركة
            systemSettings.companyName = document.getElementById('company-name').value.trim();
            systemSettings.companyAddress = document.getElementById('company-address').value.trim();
            systemSettings.companyPhone = document.getElementById('company-phone').value.trim();
            systemSettings.companyEmail = document.getElementById('company-email').value.trim();
            systemSettings.companyWebsite = document.getElementById('company-website').value.trim();
            systemSettings.companyCR = document.getElementById('company-cr').value.trim();
            systemSettings.companyTax = document.getElementById('company-tax').value.trim();

            // حفظ إعدادات الطباعة
            systemSettings.showLogoInPrint = document.getElementById('show-logo-print').checked;
            systemSettings.showCompanyInfo = document.getElementById('show-company-info').checked;
            systemSettings.showDeveloperInfo = document.getElementById('show-developer-info').checked;
            systemSettings.logoSize = document.getElementById('logo-size').value;

            // حفظ معلومات المطور
            systemSettings.developerName = document.getElementById('developer-name').value.trim();
            systemSettings.developerPhone = document.getElementById('developer-phone').value.trim();
            systemSettings.systemDescription = document.getElementById('system-description').value.trim();

            // حفظ إعدادات خلفية الصفحة الرئيسية
            systemSettings.showBackgroundLogo = document.getElementById('show-background-logo').checked;
            systemSettings.backgroundOpacity = document.getElementById('background-opacity').value;
            systemSettings.backgroundSize = document.getElementById('background-size').value;
            systemSettings.backgroundAnimation = document.getElementById('background-animation').checked;
            systemSettings.backgroundPosition = document.getElementById('background-position').value;

            // حفظ في localStorage
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

            // إشعار النافذة الرئيسية بتحديث اللوجو
            if (window.opener && window.opener.updateMainPageLogo) {
                window.opener.updateMainPageLogo();
            }

            // إشعار النافذة الرئيسية بتحديث إعدادات الطباعة
            if (window.opener && window.opener.updatePrintSettings) {
                window.opener.updatePrintSettings();
            }

            showNotification('تم حفظ الإعدادات بنجاح! ستظهر التغييرات في جميع المستندات المطبوعة وخلفية الصفحة.', 'success');
        }

        // استعادة الإعدادات الافتراضية
        function resetSettings() {
            if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
                systemSettings = {
                    companyName: 'شركة الدهان المتخصصة',
                    companyAddress: 'القدس - فلسطين',
                    companyPhone: '02-1234567',
                    companyEmail: '<EMAIL>',
                    companyWebsite: 'www.paintcompany.ps',
                    companyCR: '1010123456',
                    companyTax: '300123456789003',
                    companyLogo: '',
                    showLogoInPrint: true,
                    showCompanyInfo: true,
                    showDeveloperInfo: true,
                    logoSize: 'medium',
                    developerName: 'المهندس محمد الأحمد',
                    developerPhone: '0501234567',
                    systemDescription: 'نظام محاسبة شركات الدهان المتكامل',
                    // إعدادات خلفية الشاشة
                    showBackgroundLogo: true,
                    backgroundOpacity: '0.08',
                    backgroundSize: '400',
                    backgroundAnimation: true,
                    backgroundPosition: 'center'
                };
                
                localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
                loadSettings();
                showNotification('تم استعادة الإعدادات الافتراضية', 'info');
            }
        }

        // تحديث المعاينة
        function updatePreview() {
            // تحديث الإعدادات من النموذج
            const currentSettings = {
                companyName: document.getElementById('company-name').value || systemSettings.companyName,
                companyAddress: document.getElementById('company-address').value || systemSettings.companyAddress,
                companyPhone: document.getElementById('company-phone').value || systemSettings.companyPhone,
                companyEmail: document.getElementById('company-email').value || systemSettings.companyEmail,
                companyWebsite: document.getElementById('company-website').value || systemSettings.companyWebsite,
                companyCR: document.getElementById('company-cr').value || systemSettings.companyCR,
                companyTax: document.getElementById('company-tax').value || systemSettings.companyTax,
                companyLogo: systemSettings.companyLogo,
                showLogoInPrint: document.getElementById('show-logo-print').checked,
                showCompanyInfo: document.getElementById('show-company-info').checked,
                showDeveloperInfo: document.getElementById('show-developer-info').checked,
                logoSize: document.getElementById('logo-size').value,
                developerName: document.getElementById('developer-name').value || systemSettings.developerName,
                developerPhone: document.getElementById('developer-phone').value || systemSettings.developerPhone,
                systemDescription: document.getElementById('system-description').value || systemSettings.systemDescription
            };

            const preview = generatePreview(currentSettings);
            document.getElementById('preview-container').innerHTML = preview;
        }

        // إنشاء معاينة التنسيق
        function generatePreview(settings) {
            const logoSizes = {
                'small': '40px',
                'medium': '60px',
                'large': '80px'
            };

            const logoSize = logoSizes[settings.logoSize] || '60px';
            const today = new Date().toLocaleDateString('ar-SA');

            return `
                <!-- رأس المستند -->
                <div style="border-bottom: 2px solid #0078d4; padding-bottom: 15px; margin-bottom: 25px;">
                    ${settings.showLogoInPrint && settings.companyLogo ? `
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                            <img src="${settings.companyLogo}" alt="لوجو الشركة" style="max-height: ${logoSize}; object-fit: contain;">
                            <div style="text-align: center; flex-grow: 1;">
                                <h3 style="color: #0078d4; margin: 0; font-size: 1.4rem;">${settings.companyName}</h3>
                                <h4 style="color: #666; margin: 5px 0; font-size: 1rem;">أمر إنتاج رقم ORD-001</h4>
                            </div>
                            <div style="text-align: right; font-size: 0.7rem; color: #888;">
                                تاريخ الطباعة:<br>${today}
                            </div>
                        </div>
                    ` : `
                        <div style="text-align: center; margin-bottom: 15px;">
                            <h3 style="color: #0078d4; margin: 0; font-size: 1.4rem;">${settings.companyName}</h3>
                            <h4 style="color: #666; margin: 5px 0; font-size: 1rem;">أمر إنتاج رقم ORD-001</h4>
                            <p style="margin: 5px 0; color: #888; font-size: 0.8rem;">تاريخ الطباعة: ${today}</p>
                        </div>
                    `}

                    ${settings.showCompanyInfo ? `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 0.75rem; color: #666; text-align: center;">
                            <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 15px;">
                                <span><strong>العنوان:</strong> ${settings.companyAddress}</span>
                                <span><strong>هاتف:</strong> ${settings.companyPhone}</span>
                                ${settings.companyEmail ? `<span><strong>إيميل:</strong> ${settings.companyEmail}</span>` : ''}
                            </div>
                            <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 15px;">
                                ${settings.companyWebsite ? `<span><strong>الموقع:</strong> ${settings.companyWebsite}</span>` : ''}
                                ${settings.companyCR ? `<span><strong>س.ت:</strong> ${settings.companyCR}</span>` : ''}
                                ${settings.companyTax ? `<span><strong>الرقم الضريبي:</strong> ${settings.companyTax}</span>` : ''}
                            </div>
                        </div>
                    ` : ''}
                </div>

                <!-- محتوى المستند (مثال) -->
                <div style="text-align: center; padding: 40px 20px; color: #666; font-style: italic;">
                    [ محتوى المستند - جداول البيانات - التفاصيل ]
                </div>

                <!-- تذييل المستند -->
                ${settings.showDeveloperInfo ? `
                    <div style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #ddd;">
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.65rem; color: #666;">
                            <div style="text-align: right;">
                                <span style="color: #888;">تم تصميم وبرمجة النظام بواسطة: </span>
                                <span style="color: #0078d4; font-weight: bold;">${settings.developerName}</span>
                            </div>
                            <div style="text-align: center;">
                                <span style="color: #666;">${settings.systemDescription}</span>
                            </div>
                            <div style="text-align: left;">
                                <span style="color: #888;">للتواصل: </span>
                                <span style="color: #0078d4; font-weight: bold;">${settings.developerPhone}</span>
                            </div>
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // نظام الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideIn 0.3s ease reverse';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>
