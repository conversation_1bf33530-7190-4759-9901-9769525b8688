<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .btn {
            background: #0078d4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #106ebe;
        }
        .logo-test {
            width: 50px;
            height: 50px;
            background: #0078d4;
            border-radius: 8px;
            display: inline-block;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>اختبار آلية الطباعة والضغط</h1>
    
    <div class="test-section">
        <h2>اختبار إعدادات اللوجو</h2>
        <p>اللوجو الحالي: <span id="logo-status">غير محدد</span></p>
        <div class="logo-test" id="logo-preview"></div>
        <button class="btn" onclick="testLogo()">اختبار اللوجو</button>
    </div>

    <div class="test-section">
        <h2>اختبار الطباعة</h2>
        <button class="btn" onclick="testPrintReport()">اختبار طباعة تقرير</button>
        <button class="btn" onclick="testPrintDocument()">اختبار طباعة مستند</button>
    </div>

    <div class="test-section">
        <h2>اختبار التاريخ الميلادي</h2>
        <p>التاريخ الحالي: <span id="current-date"></span></p>
        <p>تاريخ اختبار: <span id="test-date"></span></p>
        <button class="btn" onclick="testDateFormat()">اختبار تنسيق التاريخ</button>
    </div>

    <div class="test-section">
        <h2>اختبار ضغط السطور ومنع كسر الكلمات</h2>
        <table id="test-table" style="width: 100%; border-collapse: collapse; table-layout: fixed;">
            <thead>
                <tr style="background: #f0f0f0;">
                    <th style="border: 1px solid #ddd; padding: 8px; width: 25%;">رقم الفاتورة</th>
                    <th style="border: 1px solid #ddd; padding: 8px; width: 30%;">العميل</th>
                    <th style="border: 1px solid #ddd; padding: 8px; width: 20%;">التاريخ</th>
                    <th style="border: 1px solid #ddd; padding: 8px; width: 15%;">المبلغ الإجمالي</th>
                    <th style="border: 1px solid #ddd; padding: 8px; width: 10%;">الحالة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">INV-001</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">شركة الدهان المتخصصة للمقاولات والديكور</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">15/01/2024</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">15,500.00 ريال</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">مدفوعة</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">INV-002</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">مؤسسة البناء الحديث للمقاولات العامة والتشطيبات</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">20/01/2024</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">8,750.50 ريال</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">جزئية</td>
                </tr>
            </tbody>
        </table>
        <button class="btn" onclick="applyCompression()">تطبيق ضغط السطور</button>
        <button class="btn" onclick="removeCompression()">إزالة ضغط السطور</button>
    </div>

    <script>
        // تحميل إعدادات النظام
        let systemSettings = JSON.parse(localStorage.getItem('systemSettings')) || {
            companyName: 'شركة الدهان المتخصصة',
            companyLogo: '',
            showLogoInPrint: true,
            logoSize: 'medium'
        };

        // اختبار اللوجو
        function testLogo() {
            const logoStatus = document.getElementById('logo-status');
            const logoPreview = document.getElementById('logo-preview');
            
            if (systemSettings.companyLogo) {
                logoStatus.textContent = 'موجود';
                logoPreview.style.backgroundImage = `url(${systemSettings.companyLogo})`;
                logoPreview.style.backgroundSize = 'cover';
                logoPreview.style.backgroundPosition = 'center';
            } else {
                logoStatus.textContent = 'غير موجود';
                logoPreview.style.backgroundImage = 'none';
            }
        }

        // اختبار طباعة التقرير
        function testPrintReport() {
            const logoData = systemSettings.companyLogo || '';
            const showLogo = systemSettings.showLogoInPrint && logoData;
            const logoSizes = { 'small': '25px', 'medium': '35px', 'large': '45px' };
            const logoSize = logoSizes[systemSettings.logoSize] || '35px';

            const reportContent = `
                <div class="report-content">
                    <h4>تقرير المبيعات المحسن</h4>
                    <table style="width: 100%; border-collapse: collapse; font-size: 5px; table-layout: fixed;">
                        <thead>
                            <tr>
                                <th style="border: 1px solid #000; padding: 0.1rem; background: #f0f0f0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">رقم الفاتورة</th>
                                <th style="border: 1px solid #000; padding: 0.1rem; background: #f0f0f0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">العميل</th>
                                <th style="border: 1px solid #000; padding: 0.1rem; background: #f0f0f0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">التاريخ</th>
                                <th style="border: 1px solid #000; padding: 0.1rem; background: #f0f0f0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">المبلغ الإجمالي</th>
                                <th style="border: 1px solid #000; padding: 0.1rem; background: #f0f0f0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">INV-001</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">شركة الدهان المتخصصة للمقاولات</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">15/01/2024</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">15,500.00 ريال</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">مدفوعة</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">INV-002</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">مؤسسة البناء الحديث للمقاولات</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">20/01/2024</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">8,750.50 ريال</td>
                                <td style="border: 1px solid #000; padding: 0.08rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">جزئية</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;

            const companyHeader = `
                <div class="report-header">
                    ${showLogo ? `
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.1rem;">
                            <img src="${logoData}" alt="لوجو الشركة" style="max-height: ${logoSize}; object-fit: contain;">
                            <div style="text-align: center; flex-grow: 1;">
                                <div style="font-size: 9px; font-weight: bold;">${systemSettings.companyName}</div>
                                <div style="font-size: 7px;">تقرير اختبار</div>
                            </div>
                            <div style="font-size: 5px;">${new Date().toLocaleDateString('ar-SA')}</div>
                        </div>
                    ` : `
                        <div style="font-size: 9px; font-weight: bold;">${systemSettings.companyName}</div>
                        <div style="font-size: 7px;">تقرير اختبار</div>
                        <div style="font-size: 5px;">${new Date().toLocaleDateString('ar-SA')}</div>
                    `}
                </div>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>اختبار طباعة التقرير</title>
                    <style>
                        body { font-size: 7px; line-height: 0.9; font-family: Arial, sans-serif; }
                        .report-header { text-align: center; margin-bottom: 0.15rem; border-bottom: 1px solid #000; }
                    </style>
                </head>
                <body>
                    ${companyHeader}
                    ${reportContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
        }

        // اختبار طباعة المستند
        function testPrintDocument() {
            alert('اختبار طباعة المستند - سيتم فتح نافذة طباعة جديدة');
            testPrintReport(); // استخدام نفس الاختبار مؤقتاً
        }

        // تطبيق ضغط السطور ومنع كسر الكلمات
        function applyCompression() {
            const table = document.getElementById('test-table');
            table.style.fontSize = '0.8rem';
            table.style.lineHeight = '1.1';
            table.style.tableLayout = 'fixed';

            const cells = table.querySelectorAll('th, td');
            cells.forEach(cell => {
                cell.style.padding = '4px';
                cell.style.lineHeight = '1.1';
                cell.style.whiteSpace = 'nowrap';
                cell.style.overflow = 'hidden';
                cell.style.textOverflow = 'ellipsis';
            });

            alert('تم تطبيق ضغط السطور ومنع كسر الكلمات');
        }

        // إزالة ضغط السطور
        function removeCompression() {
            const table = document.getElementById('test-table');
            table.style.fontSize = '';
            table.style.lineHeight = '';
            table.style.tableLayout = '';

            const cells = table.querySelectorAll('th, td');
            cells.forEach(cell => {
                cell.style.padding = '8px';
                cell.style.lineHeight = '';
                cell.style.whiteSpace = '';
                cell.style.overflow = '';
                cell.style.textOverflow = '';
            });

            alert('تم إزالة ضغط السطور');
        }

        // دالة تنسيق التاريخ الميلادي (نفس الدالة في النظام)
        function formatDate(dateString) {
            const date = new Date(dateString);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        // اختبار تنسيق التاريخ
        function testDateFormat() {
            const currentDate = new Date().toISOString().split('T')[0];
            const testDate = '2024-01-15';

            document.getElementById('current-date').textContent = formatDate(currentDate);
            document.getElementById('test-date').textContent = formatDate(testDate);

            alert('تم تحديث التواريخ بالتنسيق الميلادي');
        }

        // تحميل الاختبارات عند بدء الصفحة
        window.onload = function() {
            testLogo();
            testDateFormat();
        };
    </script>
</body>
</html>
