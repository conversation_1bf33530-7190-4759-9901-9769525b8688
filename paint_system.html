<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة الدهان المتكامل</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', sans-serif;
            background: #0078d4;
            color: #333;
            direction: rtl;
            overflow: hidden;
        }

        /* Login Screen */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .login-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            animation: loginSlideIn 0.8s ease-out;
        }

        @keyframes loginSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
        }

        .login-header h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }

        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #0078d4;
            box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 1;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #106ebe 0%, #005a9e 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.4);
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            border-radius: 10px;
            margin-top: 1rem;
        }

        .login-footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            font-size: 0.8rem;
            color: #666;
        }

        /* Main App */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background: #f3f3f3;
            overflow: hidden;
            font-family: 'Segoe UI', 'Cairo', sans-serif;
        }

        .header {
            background: #323130;
            color: white;
            padding: 0.75rem 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-bottom: 1px solid #605e5c;
            z-index: 1000;
            position: relative;
            height: 48px;
            display: flex;
            align-items: center;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 100%;
            width: 100%;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo i {
            font-size: 1.5rem;
            color: #0078d4;
        }

        .company-info h1 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .user-info {
            font-size: 0.7rem;
            color: #a0aec0;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .currency-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
        }

        .btn-secondary {
            background-color: #718096;
            color: white;
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .btn-danger {
            background-color: #e53e3e;
            color: white;
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e53e3e;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            right: 0;
            top: 48px;
            width: 280px;
            height: calc(100vh - 48px);
            background: #1a202c;
            color: white;
            overflow-y: auto;
            z-index: 999;
            border-left: 1px solid #2d3748;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #2d3748;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .username {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-role {
            font-size: 0.7rem;
            color: #a0aec0;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
        }

        .nav-category {
            padding: 1rem 1.5rem 0.5rem;
        }

        .category-title {
            font-size: 0.7rem;
            font-weight: 600;
            color: #a0aec0;
            text-transform: uppercase;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 1.5rem;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: #2d3748;
            color: #0078d4;
            border-right-color: #0078d4;
        }

        .nav-link.active {
            background-color: #0078d4;
            color: white;
            border-right-color: #0078d4;
        }

        .nav-link i {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: calc(100vh - 48px);
            background: #f7fafc;
            overflow-y: auto;
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.3s ease-out;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .dashboard-header h2 {
            font-size: 1.8rem;
            color: #2d3748;
            font-weight: 600;
        }

        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.3s ease;
            border-left: 4px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .stat-card.primary { border-left-color: #0078d4; }
        .stat-card.success { border-left-color: #48bb78; }
        .stat-card.warning { border-left-color: #ed8936; }
        .stat-card.info { border-left-color: #38b2ac; }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-card.primary .stat-icon { background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%); }
        .stat-card.success .stat-icon { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); }
        .stat-card.warning .stat-icon { background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); }
        .stat-card.info .stat-icon { background: linear-gradient(135deg, #38b2ac 0%, #319795 100%); }

        .stat-content h3 {
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 0.5rem;
        }

        .main-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
        }

        .sub-number {
            font-size: 0.8rem;
            color: #718096;
            margin-right: 0.5rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .dashboard-widget {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: #f7fafc;
        }

        .widget-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 1.5rem;
        }

        .quick-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1.5rem;
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #4a5568;
        }

        .quick-action-btn:hover {
            background: #0078d4;
            color: white;
            border-color: #0078d4;
            transform: translateY(-2px);
        }

        .quick-action-btn i {
            font-size: 1.5rem;
        }

        .quick-action-btn span {
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
        }

        .activity-list {
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #a0aec0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="company-logo">
                    <i class="fas fa-paint-brush"></i>
                </div>
                <h1>نظام إدارة شركة الدهان</h1>
                <p>نظام متكامل لإدارة شركات دهان الأثاث الخشبي</p>
            </div>
            <form id="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" id="username" class="form-input" value="admin" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" id="password" class="form-input" value="admin123" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </form>
            <div class="login-footer">
                <p>المطور: فارس نواف | <EMAIL> | 0569329925</p>
                <p style="margin-top: 10px; font-size: 0.7rem;">
                    <strong>بيانات التجربة:</strong><br>
                    المدير: admin / admin123<br>
                    المستخدم: user / user123
                </p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-paint-brush"></i>
                    <div class="company-info">
                        <h1>نظام إدارة شركة الدهان</h1>
                        <span id="current-user" class="user-info"></span>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="currency-selector">
                        <select id="currency-selector" class="currency-select" onchange="changeCurrency()">
                            <option value="SAR">ريال سعودي</option>
                            <option value="USD">دولار أمريكي</option>
                            <option value="EUR">يورو</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" onclick="exportData()" title="تصدير">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="openNotifications()" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count" class="notification-badge">3</span>
                    </button>
                    <button class="btn btn-danger" onclick="logout()" title="خروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span id="sidebar-username" class="username"></span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-category">
                    <span class="category-title">الإدارة الرئيسية</span>
                </li>
                <li><a href="#" class="nav-link active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة العلاقات</span>
                </li>
                <li><a href="#" class="nav-link" onclick="showSection('customers')">
                    <i class="fas fa-users"></i> العملاء
                </a></li>
                <li><a href="#" class="nav-link" onclick="showSection('suppliers')">
                    <i class="fas fa-truck"></i> الموردين
                </a></li>
                <li><a href="#" class="nav-link" onclick="showSection('partners')">
                    <i class="fas fa-handshake"></i> الشركاء
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة المخزون</span>
                </li>
                <li><a href="#" class="nav-link" onclick="showSection('items')">
                    <i class="fas fa-boxes"></i> الأصناف
                </a></li>

                <li class="nav-category">
                    <span class="category-title">العمليات</span>
                </li>
                <li><a href="#" class="nav-link" onclick="showSection('orders')">
                    <i class="fas fa-clipboard-list"></i> أوامر الإنتاج
                </a></li>

                <li class="nav-category">
                    <span class="category-title">المالية</span>
                </li>
                <li><a href="#" class="nav-link" onclick="showSection('invoices')">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a></li>
                <li><a href="#" class="nav-link" onclick="showSection('payments')">
                    <i class="fas fa-money-bill-wave"></i> المدفوعات
                </a></li>

                <li class="nav-category">
                    <span class="category-title">التقارير</span>
                </li>
                <li><a href="#" class="nav-link" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-header">
                    <h2>لوحة التحكم</h2>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-stats">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3>أوامر الإنتاج</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-orders">5</span>
                                <span class="sub-number">طلبية</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3>إجمالي المبيعات</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-sales">25,750</span>
                                <span class="sub-number" id="sales-currency">ريال</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>العملاء</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-customers">12</span>
                                <span class="sub-number">عميل</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content">
                            <h3>المخزون</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-inventory">8</span>
                                <span class="sub-number">صنف</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Quick Actions -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>إجراءات سريعة</h3>
                        </div>
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة أمر إنتاج جديد')">
                                <i class="fas fa-plus"></i>
                                <span>أمر إنتاج جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة عميل جديد')">
                                <i class="fas fa-user-plus"></i>
                                <span>عميل جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة صنف جديد')">
                                <i class="fas fa-box"></i>
                                <span>صنف جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم تسجيل دفعة جديدة')">
                                <i class="fas fa-money-bill"></i>
                                <span>تسجيل دفعة</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>الأنشطة الحديثة</h3>
                        </div>
                        <div class="activity-list">
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #0078d4; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">أمر إنتاج جديد #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">العميل: أحمد محمد</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعة</div>
                                </div>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #48bb78; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">فاتورة جديدة #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">المبلغ: 5,250 ريال</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعتين</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other Sections -->
            <section id="customers" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-users" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم العملاء</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة شاملة لجميع العملاء ومعاملاتهم</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                </div>
            </section>

            <section id="suppliers" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-truck" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الموردين</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الموردين والمشتريات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </button>
                </div>
            </section>

            <section id="partners" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-handshake" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الشركاء</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الشركاء وتوزيع الأرباح</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة شريك جديد
                    </button>
                </div>
            </section>

            <section id="items" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-boxes" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الأصناف</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة أصناف الدهانات والمنتجات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </button>
                </div>
            </section>

            <section id="orders" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-clipboard-list" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">أوامر الإنتاج</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة أوامر الإنتاج وحساب التكاليف</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
                    </button>
                </div>
            </section>

            <section id="invoices" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-file-invoice" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">الفواتير</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الفواتير والمقبوضات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                    </button>
                </div>
            </section>

            <section id="payments" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-money-bill-wave" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">المدفوعات</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة المقبوضات والمدفوعات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة دفعة جديدة
                    </button>
                </div>
            </section>

            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2>التقارير</h2>
                    <p>تقارير شاملة ومفصلة لجميع عمليات الشركة</p>
                </div>

                <div class="reports-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-top: 2rem;">
                    <div class="report-card" onclick="generateSalesReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-chart-line" style="font-size: 2.5rem; color: #0078d4; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المبيعات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير شامل عن المبيعات والإيرادات</p>
                    </div>

                    <div class="report-card" onclick="generateCustomersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-users" style="font-size: 2.5rem; color: #38a169; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير العملاء</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير عن أرصدة ومعاملات العملاء</p>
                    </div>

                    <div class="report-card" onclick="generateCustomerBalanceReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-balance-scale" style="font-size: 2.5rem; color: #805ad5; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير أرصدة العملاء</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير مفصل عن أرصدة العملاء والمديونيات</p>
                    </div>

                    <div class="report-card" onclick="generateSuppliersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-truck" style="font-size: 2.5rem; color: #ed8936; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الموردين</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير عن أرصدة ومعاملات الموردين</p>
                    </div>

                    <div class="report-card" onclick="generateProfitReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-money-bill-wave" style="font-size: 2.5rem; color: #4299e1; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الأرباح</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير توزيع الأرباح على الشركاء</p>
                    </div>

                    <div class="report-card" onclick="generateInventoryReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-boxes" style="font-size: 2.5rem; color: #805ad5; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المخزون</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير شامل عن حالة المخزون والكميات</p>
                    </div>

                    <div class="report-card" onclick="generateProductionReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-cogs" style="font-size: 2.5rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الإنتاج</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير أوامر الإنتاج وحالة التنفيذ</p>
                    </div>

                    <div class="report-card" onclick="generatePaymentsReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-credit-card" style="font-size: 2.5rem; color: #319795; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المدفوعات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير المدفوعات وطرق الدفع</p>
                    </div>

                    <div class="report-card" onclick="generateExpensesReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-receipt" style="font-size: 2.5rem; color: #d69e2e; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المصروفات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير المصروفات والتكاليف التشغيلية</p>
                    </div>

                    <div class="report-card" onclick="generatePartnersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-handshake" style="font-size: 2.5rem; color: #9f7aea; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الشركاء</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير الشركاء ونسب الأرباح</p>
                    </div>

                    <div class="report-card" onclick="generateFinancialReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-chart-pie" style="font-size: 2.5rem; color: #f56565; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">التقرير المالي الشامل</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير مالي شامل ومفصل للشركة</p>
                    </div>
                </div>

                <style>
                    .report-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
                    }
                </style>
            </section>
        </main>
    </div>

    <script>
        // Global Variables
        let currentUser = null;
        let currentCurrency = 'SAR';

        // Exchange rates (relative to SAR)
        const exchangeRates = {
            'SAR': 1,
            'USD': 0.27,
            'EUR': 0.24
        };

        // Currency names
        const currencyNames = {
            'SAR': 'ريال',
            'USD': 'دولار',
            'EUR': 'يورو'
        };

        // Sample users
        const users = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                fullName: 'فارس نواف',
                role: 'admin'
            },
            {
                id: 2,
                username: 'user',
                password: 'user123',
                fullName: 'مستخدم تجريبي',
                role: 'user'
            }
        ];

        // Initialize App
        document.addEventListener('DOMContentLoaded', function() {
            console.log('App initialized');
        });

        // Login System
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const user = users.find(u => u.username === username && u.password === password);

            if (user) {
                currentUser = user;
                showMainApp();
                showNotification('تم تسجيل الدخول بنجاح', 'success');
            } else {
                showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        function showMainApp() {
            document.getElementById('login-screen').style.display = 'none';
            document.getElementById('main-app').style.display = 'flex';

            // Update UI with user info
            document.getElementById('current-user').textContent = currentUser.fullName;
            document.getElementById('sidebar-username').textContent = currentUser.fullName;
            document.getElementById('currency-selector').value = currentCurrency;
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentUser = null;
                document.getElementById('login-screen').style.display = 'flex';
                document.getElementById('main-app').style.display = 'none';
                showNotification('تم تسجيل الخروج بنجاح', 'success');
            }
        }

        // Navigation System
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Add active class to clicked nav link
            const activeLink = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            console.log('Switched to section:', sectionId);
        }

        // Currency Functions
        function changeCurrency() {
            const selector = document.getElementById('currency-selector');
            currentCurrency = selector.value;
            updateAllAmounts();
            showNotification(`تم تغيير العملة إلى ${currencyNames[currentCurrency]}`, 'success');
        }

        function updateAllAmounts() {
            // Update total sales
            const salesElement = document.getElementById('total-sales');
            if (salesElement) {
                const originalAmount = 25750; // Original amount in SAR
                const convertedAmount = convertCurrency(originalAmount, 'SAR', currentCurrency);
                salesElement.textContent = formatCurrency(convertedAmount);
            }

            // Update currency display
            const currencyElement = document.getElementById('sales-currency');
            if (currencyElement) {
                currencyElement.textContent = currencyNames[currentCurrency];
            }
        }

        function convertCurrency(amount, fromCurrency, toCurrency) {
            // Convert to SAR first
            const amountInSAR = amount / exchangeRates[fromCurrency];
            // Then convert to target currency
            return amountInSAR * exchangeRates[toCurrency];
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(Math.round(amount));
        }

        // Utility Functions
        function refreshDashboard() {
            showNotification('تم تحديث لوحة التحكم', 'success');
        }

        function exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                currentUser: currentUser,
                currentCurrency: currentCurrency
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `paint-system-export-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح', 'success');
        }

        function openNotifications() {
            showNotification('لا توجد إشعارات جديدة', 'info');
        }

        function showMessage(message) {
            showNotification(message, 'info');
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: white;
                padding: 1rem 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 3000;
                display: flex;
                align-items: center;
                gap: 1rem;
                min-width: 300px;
                animation: slideDown 0.3s ease-out;
                border-right: 4px solid ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            `;

            const icon = document.createElement('i');
            icon.className = `fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}`;

            const text = document.createElement('span');
            text.textContent = message;

            notification.appendChild(icon);
            notification.appendChild(text);
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

    <!-- Main Application -->
    <div id="main-app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-paint-brush"></i>
                    <div class="company-info">
                        <h1 id="header-company-name">نظام إدارة شركة الدهان</h1>
                        <span id="current-user" class="user-info"></span>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="currency-selector">
                        <select id="currency-selector" class="currency-select" onchange="changeCurrency(this.value)">
                            <option value="SAR">ريال سعودي</option>
                            <option value="USD">دولار أمريكي</option>
                            <option value="EUR">يورو</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" onclick="exportAllData()" title="تصدير البيانات">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="openNotifications()" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count" class="notification-badge">3</span>
                    </button>
                    <button class="btn btn-danger" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span id="sidebar-username" class="username"></span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-category">
                    <span class="category-title">الإدارة الرئيسية</span>
                </li>
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة العلاقات</span>
                </li>
                <li><a href="#customers" class="nav-link" data-section="customers" onclick="showSection('customers')">
                    <i class="fas fa-users"></i> العملاء
                </a></li>
                <li><a href="#suppliers" class="nav-link" data-section="suppliers" onclick="showSection('suppliers')">
                    <i class="fas fa-truck"></i> الموردين
                </a></li>
                <li><a href="#partners" class="nav-link" data-section="partners" onclick="showSection('partners')">
                    <i class="fas fa-handshake"></i> الشركاء
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة المخزون</span>
                </li>
                <li><a href="#items" class="nav-link" data-section="items" onclick="showSection('items')">
                    <i class="fas fa-boxes"></i> الأصناف
                </a></li>

                <li class="nav-category">
                    <span class="category-title">العمليات</span>
                </li>
                <li><a href="#orders" class="nav-link" data-section="orders" onclick="showSection('orders')">
                    <i class="fas fa-clipboard-list"></i> أوامر الإنتاج
                </a></li>

                <li class="nav-category">
                    <span class="category-title">المالية</span>
                </li>
                <li><a href="#invoices" class="nav-link" data-section="invoices" onclick="showSection('invoices')">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a></li>
                <li><a href="#payments" class="nav-link" data-section="payments" onclick="showSection('payments')">
                    <i class="fas fa-money-bill-wave"></i> المدفوعات
                </a></li>

                <li class="nav-category">
                    <span class="category-title">التقارير</span>
                </li>
                <li><a href="#reports" class="nav-link" data-section="reports" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-header">
                    <h2>لوحة التحكم</h2>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-stats">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3>أوامر الإنتاج</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-orders">5</span>
                                <span class="sub-number">طلبية</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3>إجمالي المبيعات</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-sales">25,750</span>
                                <span class="sub-number" id="sales-currency">ريال</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>العملاء</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-customers">12</span>
                                <span class="sub-number">عميل</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content">
                            <h3>المخزون</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-inventory">8</span>
                                <span class="sub-number">صنف</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Quick Actions -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>إجراءات سريعة</h3>
                        </div>
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة أمر إنتاج جديد')">
                                <i class="fas fa-plus"></i>
                                <span>أمر إنتاج جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة عميل جديد')">
                                <i class="fas fa-user-plus"></i>
                                <span>عميل جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة صنف جديد')">
                                <i class="fas fa-box"></i>
                                <span>صنف جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم تسجيل دفعة جديدة')">
                                <i class="fas fa-money-bill"></i>
                                <span>تسجيل دفعة</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>الأنشطة الحديثة</h3>
                        </div>
                        <div class="activity-list" id="recent-activities">
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #0078d4; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">أمر إنتاج جديد #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">العميل: أحمد محمد</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعة</div>
                                </div>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #48bb78; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">فاتورة جديدة #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">المبلغ: 5,250 ريال</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعتين</div>
                                </div>
                            </div>
                            <div style="padding: 1rem; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #ed8936; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">مقبوض جديد</div>
                                    <div style="font-size: 0.8rem; color: #718096;">المبلغ: 3,000 ريال</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ 3 ساعات</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

    <!-- Main Application -->
    <div id="main-app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-paint-brush"></i>
                    <div class="company-info">
                        <h1 id="header-company-name">نظام إدارة شركة الدهان</h1>
                        <span id="current-user" class="user-info"></span>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="currency-selector">
                        <select id="currency-selector" class="currency-select" onchange="changeCurrency(this.value)">
                            <option value="SAR">ريال سعودي</option>
                            <option value="USD">دولار أمريكي</option>
                            <option value="EUR">يورو</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" onclick="exportAllData()" title="تصدير البيانات">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="openNotifications()" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count" class="notification-badge">0</span>
                    </button>
                    <button class="btn btn-danger" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span id="sidebar-username" class="username"></span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-category">
                    <span class="category-title">الإدارة الرئيسية</span>
                </li>
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة العلاقات</span>
                </li>
                <li><a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i> العملاء
                </a></li>
                <li><a href="#suppliers" class="nav-link" data-section="suppliers">
                    <i class="fas fa-truck"></i> الموردين
                </a></li>
                <li><a href="#partners" class="nav-link" data-section="partners">
                    <i class="fas fa-handshake"></i> الشركاء
                </a></li>

                <li class="nav-category">
                    <span class="category-title">إدارة المخزون</span>
                </li>
                <li><a href="#items" class="nav-link" data-section="items">
                    <i class="fas fa-boxes"></i> الأصناف
                </a></li>

                <li class="nav-category">
                    <span class="category-title">العمليات</span>
                </li>
                <li><a href="#orders" class="nav-link" data-section="orders">
                    <i class="fas fa-clipboard-list"></i> أوامر الإنتاج
                </a></li>

                <li class="nav-category">
                    <span class="category-title">المالية</span>
                </li>
                <li><a href="#invoices" class="nav-link" data-section="invoices">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a></li>
                <li><a href="#payments" class="nav-link" data-section="payments">
                    <i class="fas fa-money-bill-wave"></i> المدفوعات
                </a></li>

                <li class="nav-category">
                    <span class="category-title">التقارير</span>
                </li>
                <li><a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-header">
                    <h2>لوحة التحكم</h2>
                    <div class="dashboard-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-stats">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3>أوامر الإنتاج</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-orders">5</span>
                                <span class="sub-number">طلبية</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3>إجمالي المبيعات</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-sales">25,750</span>
                                <span class="sub-number" id="sales-currency">ريال</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>العملاء</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-customers">12</span>
                                <span class="sub-number">عميل</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content">
                            <h3>المخزون</h3>
                            <div class="stat-numbers">
                                <span class="main-number" id="total-inventory">8</span>
                                <span class="sub-number">صنف</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Quick Actions -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>إجراءات سريعة</h3>
                        </div>
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة أمر إنتاج جديد')">
                                <i class="fas fa-plus"></i>
                                <span>أمر إنتاج جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة عميل جديد')">
                                <i class="fas fa-user-plus"></i>
                                <span>عميل جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم إضافة صنف جديد')">
                                <i class="fas fa-box"></i>
                                <span>صنف جديد</span>
                            </button>
                            <button class="quick-action-btn" onclick="showMessage('سيتم تسجيل دفعة جديدة')">
                                <i class="fas fa-money-bill"></i>
                                <span>تسجيل دفعة</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>الأنشطة الحديثة</h3>
                        </div>
                        <div class="activity-list" id="recent-activities">
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #0078d4; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">أمر إنتاج جديد #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">العميل: أحمد محمد</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعة</div>
                                </div>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #48bb78; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">فاتورة جديدة #001</div>
                                    <div style="font-size: 0.8rem; color: #718096;">المبلغ: 5,250 ريال</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ ساعتين</div>
                                </div>
                            </div>
                            <div style="padding: 1rem; display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #ed8936; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: #2d3748;">مقبوض جديد</div>
                                    <div style="font-size: 0.8rem; color: #718096;">المبلغ: 3,000 ريال</div>
                                    <div style="font-size: 0.7rem; color: #a0aec0;">منذ 3 ساعات</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other Sections -->
            <section id="customers" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-users" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم العملاء</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة شاملة لجميع العملاء ومعاملاتهم</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                </div>
            </section>

            <section id="suppliers" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-truck" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الموردين</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الموردين والمشتريات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة مورد جديد
                    </button>
                </div>
            </section>

            <section id="partners" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-handshake" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الشركاء</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الشركاء وتوزيع الأرباح</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة شريك جديد
                    </button>
                </div>
            </section>

            <section id="items" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-boxes" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">قسم الأصناف</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة أصناف الدهانات والمنتجات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </button>
                </div>
            </section>

            <section id="orders" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-clipboard-list" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">أوامر الإنتاج</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة أوامر الإنتاج وحساب التكاليف</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
                    </button>
                </div>
            </section>

            <section id="invoices" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-file-invoice" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">الفواتير</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة الفواتير والمقبوضات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                    </button>
                </div>
            </section>

            <section id="payments" class="content-section">
                <div style="text-align: center; padding: 4rem; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <i class="fas fa-money-bill-wave" style="font-size: 4rem; color: #0078d4; margin-bottom: 1rem;"></i>
                    <h2 style="color: #2d3748; margin-bottom: 1rem;">المدفوعات</h2>
                    <p style="color: #718096; margin-bottom: 2rem;">إدارة المقبوضات والمدفوعات</p>
                    <button class="btn btn-primary" onclick="showMessage('سيتم تطوير هذا القسم قريباً')">
                        <i class="fas fa-plus"></i> إضافة دفعة جديدة
                    </button>
                </div>
            </section>

            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2>التقارير</h2>
                    <p>تقارير شاملة ومفصلة لجميع عمليات الشركة</p>
                </div>

                <div class="reports-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-top: 2rem;">
                    <div class="report-card" onclick="generateSalesReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-chart-line" style="font-size: 2.5rem; color: #0078d4; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المبيعات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير شامل عن المبيعات والإيرادات</p>
                    </div>

                    <div class="report-card" onclick="generateCustomersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-users" style="font-size: 2.5rem; color: #38a169; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير العملاء</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير عن أرصدة ومعاملات العملاء</p>
                    </div>

                    <div class="report-card" onclick="generateSuppliersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-truck" style="font-size: 2.5rem; color: #ed8936; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الموردين</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير عن أرصدة ومعاملات الموردين</p>
                    </div>

                    <div class="report-card" onclick="generateProfitReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-money-bill-wave" style="font-size: 2.5rem; color: #4299e1; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الأرباح</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير توزيع الأرباح على الشركاء</p>
                    </div>

                    <div class="report-card" onclick="generateInventoryReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-boxes" style="font-size: 2.5rem; color: #805ad5; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المخزون</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير شامل عن حالة المخزون والكميات</p>
                    </div>

                    <div class="report-card" onclick="generateProductionReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-cogs" style="font-size: 2.5rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الإنتاج</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير أوامر الإنتاج وحالة التنفيذ</p>
                    </div>

                    <div class="report-card" onclick="generatePaymentsReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-credit-card" style="font-size: 2.5rem; color: #319795; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المدفوعات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير المدفوعات وطرق الدفع</p>
                    </div>

                    <div class="report-card" onclick="generateExpensesReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-receipt" style="font-size: 2.5rem; color: #d69e2e; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير المصروفات</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير المصروفات والتكاليف التشغيلية</p>
                    </div>

                    <div class="report-card" onclick="generatePartnersReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-handshake" style="font-size: 2.5rem; color: #9f7aea; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">تقرير الشركاء</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير الشركاء ونسب الأرباح</p>
                    </div>

                    <div class="report-card" onclick="generateFinancialReport()" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); cursor: pointer; transition: transform 0.2s; text-align: center;">
                        <i class="fas fa-chart-pie" style="font-size: 2.5rem; color: #f56565; margin-bottom: 1rem;"></i>
                        <h3 style="color: #2d3748; margin-bottom: 0.5rem;">التقرير المالي الشامل</h3>
                        <p style="color: #718096; font-size: 0.9rem;">تقرير مالي شامل ومفصل للشركة</p>
                    </div>
                </div>

                <style>
                    .report-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
                    }
                </style>
            </section>
        </main>
    </div>

    <script>
        // Global Variables
        let currentUser = null;
        let systemSettings = {
            baseCurrency: 'SAR',
            companyName: 'شركة الدهان المتخصصة'
        };

        // Sample users
        const users = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                fullName: 'فارس نواف',
                role: 'admin'
            },
            {
                id: 2,
                username: 'user',
                password: 'user123',
                fullName: 'مستخدم تجريبي',
                role: 'user'
            }
        ];

        // Initialize App
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            updateNotificationCount();
        });

        // Login System
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const user = users.find(u => u.username === username && u.password === password);

            if (user) {
                currentUser = user;
                showMainApp();
                showNotification('تم تسجيل الدخول بنجاح', 'success');
            } else {
                showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        function showMainApp() {
            document.getElementById('login-screen').style.display = 'none';
            document.getElementById('main-app').style.display = 'flex';

            // Update UI with user info
            document.getElementById('current-user').textContent = currentUser.fullName;
            document.getElementById('sidebar-username').textContent = currentUser.fullName;
            document.getElementById('currency-selector').value = systemSettings.baseCurrency;
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentUser = null;
                document.getElementById('login-screen').style.display = 'flex';
                document.getElementById('main-app').style.display = 'none';
                showNotification('تم تسجيل الخروج بنجاح', 'success');
            }
        }

        // Navigation
        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.content-section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links and sections
                    navLinks.forEach(l => l.classList.remove('active'));
                    sections.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Show corresponding section
                    const sectionId = this.getAttribute('data-section');
                    const section = document.getElementById(sectionId);
                    if (section) {
                        section.classList.add('active');
                    }
                });
            });
        }

        // Currency Functions
        function changeCurrency(newCurrency) {
            systemSettings.baseCurrency = newCurrency;
            document.getElementById('sales-currency').textContent = getCurrencyName(newCurrency);
            showNotification(`تم تغيير العملة إلى ${getCurrencyName(newCurrency)}`, 'success');
        }

        function getCurrencyName(currency) {
            const currencies = {
                'SAR': 'ريال',
                'USD': 'دولار',
                'EUR': 'يورو'
            };
            return currencies[currency] || currency;
        }

        // Utility Functions
        function refreshDashboard() {
            showNotification('تم تحديث لوحة التحكم', 'success');
        }

        function exportAllData() {
            const data = {
                timestamp: new Date().toISOString(),
                systemSettings: systemSettings,
                currentUser: currentUser
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `paint-system-export-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح', 'success');
        }

        function openNotifications() {
            showNotification('لا توجد إشعارات جديدة', 'info');
        }

        function updateNotificationCount() {
            document.getElementById('notification-count').textContent = '3';
        }

        function showMessage(message) {
            showNotification(message, 'info');
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: white;
                padding: 1rem 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 3000;
                display: flex;
                align-items: center;
                gap: 1rem;
                min-width: 300px;
                animation: slideDown 0.3s ease-out;
                border-right: 4px solid ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            `;

            const icon = document.createElement('i');
            icon.className = `fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}`;

            const text = document.createElement('span');
            text.textContent = message;

            notification.appendChild(icon);
            notification.appendChild(text);
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>