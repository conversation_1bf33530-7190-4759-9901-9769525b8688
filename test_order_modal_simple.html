<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة أمر الإنتاج المحسنة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: #f0f2f5;
            font-family: 'Segoe UI', 'Cairo', sans-serif;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            margin: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
        }
        
        .modal {
            display: block !important;
        }
        
        h1 {
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        p {
            color: #718096;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-cogs"></i> اختبار نافذة أمر الإنتاج المحسنة</h1>
        <p>انقر على الزر أدناه لفتح النافذة المحسنة بتصميم احترافي مضغوط</p>
        
        <button class="test-button" onclick="openOrderModal()">
            <i class="fas fa-plus"></i>
            فتح نافذة أمر إنتاج جديد
        </button>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script src="script.js"></script>
    
    <script>
        // Initialize sample data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Make sure sample data is loaded
            if (typeof addSampleData === 'function') {
                addSampleData();
            }
        });
        
        // Override closeModal to work with our test
        function closeModal(modalId) {
            document.getElementById('modal-container').innerHTML = '';
        }
    </script>
</body>
</html>
