<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة الدهان المتكامل - الإصدار 5.0 (النظام المتكامل النهائي)</title>

    <!--
    ===============================================================================
    🎨 نظام إدارة شركة الدهان المتكامل - الإصدار 5.0 (النظام المتكامل النهائي)
    ===============================================================================
    
    📊 النظام مكتمل 100% - جميع الأقسام والوظائف متاحة
    💰 العملة الافتراضية: الشيكل الإسرائيلي (₪)
    🌐 دعم متعدد اللغات: العربية، العبرية، الإنجليزية
    🔒 نظام أمان متقدم مع نسخ احتياطي
    📱 تصميم متجاوب لجميع الأجهزة
    
    تم التطوير بواسطة: المهندس faresnawaf | 0569329925
    ===============================================================================
    -->

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', sans-serif;
            background: #0078d4;
            color: #333;
            direction: rtl;
            overflow: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .loading-logo i {
            font-size: 4rem;
            color: white;
        }

        .loading-text {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: loading 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* Login Screen */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .login-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            animation: loginSlideIn 0.8s ease-out;
        }

        @keyframes loginSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .company-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-logo-circle {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
            animation: pulse 2s infinite;
        }

        .login-header h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }

        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: #0078d4;
            box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 1;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #106ebe 0%, #005a9e 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.4);
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            border-radius: 10px;
            margin-top: 1rem;
        }

        .login-footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            font-size: 0.8rem;
            color: #666;
        }

        /* Main App Styles */
        .app-container {
            display: none;
            flex-direction: column;
            min-height: 100vh;
            background: #f3f3f3;
            overflow: hidden;
            font-family: 'Segoe UI', 'Cairo', sans-serif;
        }

        .header {
            background: #323130;
            color: white;
            padding: 0.75rem 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-bottom: 1px solid #605e5c;
            z-index: 1000;
            position: relative;
            height: 48px;
            display: flex;
            align-items: center;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 100%;
            width: 100%;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .company-info h1 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .company-info p {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .currency-select {
            background: #484644;
            color: white;
            border: 1px solid #605e5c;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .main-container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: #f8f9fa;
            border-left: 1px solid #e2e8f0;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
        }

        .menu-item:hover {
            background: #e2e8f0;
            color: #2d3748;
        }

        .menu-item.active {
            background: #0078d4;
            color: white;
        }

        .menu-item i {
            margin-left: 0.5rem;
            width: 16px;
        }

        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 2rem;
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: #2d3748;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 48px;
                height: calc(100vh - 48px);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(280px);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .login-box {
                width: 90%;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-logo">
            <i class="fas fa-palette"></i>
        </div>
        <div class="loading-text">نظام إدارة شركة الدهان المتكامل</div>
        <div class="loading-subtitle">الإصدار 5.0 - النظام المتكامل النهائي</div>
        <div class="loading-progress">
            <div class="loading-progress-bar"></div>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-container" class="login-container">
        <div class="login-box">
            <div class="company-logo">
                <div class="login-logo-circle">
                    <i class="fas fa-palette"></i>
                </div>
            </div>
            <div class="login-header">
                <h1>نظام إدارة شركة الدهان</h1>
                <p>الإصدار 5.0 - النظام المتكامل النهائي</p>
            </div>
            <form onsubmit="login(event)">
                <div class="form-group">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" id="username" class="form-input" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" id="password" class="form-input" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>
            <div class="login-footer">
                <p>تم التطوير بواسطة: المهندس faresnawaf | 0569329925</p>
                <p>العملة الافتراضية: الشيكل الإسرائيلي (₪)</p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app-container" class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="company-info">
                        <h1 id="company-name-header">شركة الدهان المتكاملة</h1>
                        <p>نظام إدارة متكامل - الإصدار 5.0</p>
                    </div>
                </div>
                <div class="header-controls">
                    <select id="currency-selector" class="currency-select" onchange="changeCurrency()">
                        <option value="ILS" selected>₪ شيكل إسرائيلي</option>
                        <option value="SAR">ر.س ريال سعودي</option>
                        <option value="USD">$ دولار أمريكي</option>
                        <option value="EUR">€ يورو</option>
                    </select>
                    <button class="btn btn-primary" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </button>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- Sidebar -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-menu">
                    <button class="menu-item active" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </button>
                    <button class="menu-item" onclick="showSection('customers')">
                        <i class="fas fa-users"></i> إدارة العملاء
                    </button>
                    <button class="menu-item" onclick="showSection('suppliers')">
                        <i class="fas fa-truck"></i> إدارة الموردين
                    </button>
                    <button class="menu-item" onclick="showSection('items')">
                        <i class="fas fa-boxes"></i> إدارة الأصناف
                    </button>
                    <button class="menu-item" onclick="showSection('paint-types')">
                        <i class="fas fa-palette"></i> أنواع الدهان
                    </button>
                    <button class="menu-item" onclick="showSection('orders')">
                        <i class="fas fa-clipboard-list"></i> أوامر الإنتاج
                    </button>
                    <button class="menu-item" onclick="showSection('invoices')">
                        <i class="fas fa-file-invoice"></i> الفواتير
                    </button>
                    <button class="menu-item" onclick="showSection('reports')">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </button>
                    <button class="menu-item" onclick="showSection('settings')">
                        <i class="fas fa-cogs"></i> الإعدادات
                    </button>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Dashboard Section -->
                <section id="dashboard" class="content-section active">
                    <div class="section-header">
                        <h2>لوحة التحكم</h2>
                    </div>
                    <div id="dashboard-content">
                        <p>جاري تحميل لوحة التحكم...</p>
                    </div>
                </section>

                <!-- Other sections will be loaded dynamically -->
                <div id="dynamic-content"></div>
            </main>
        </div>
    </div>

    <!-- Load JavaScript -->
    <script src="paint_system_v5.0_functions.js"></script>
</body>
</html>
