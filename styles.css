* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Cairo', sans-serif;
    background: #0078d4;
    color: #333;
    direction: rtl;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

/* Login Screen Styles */
.login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    animation: loginSlideIn 0.8s ease-out;
}

@keyframes loginSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.login-header {
    margin-bottom: 2rem;
}

.company-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
}

.login-header h1 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
    text-align: right;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.btn-login {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 10px;
    margin-top: 1rem;
}

.login-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.8rem;
    color: #666;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f3f3f3;
    overflow: hidden;
    font-family: 'Segoe UI', 'Cairo', sans-serif;
}

/* Header Styles */
.header {
    background: #323130;
    color: white;
    padding: 0.75rem 2rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12);
    border-bottom: 1px solid #605e5c;
    z-index: 1000;
    position: relative;
    height: 48px;
    display: flex;
    align-items: center;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.logo i {
    font-size: 2rem;
    color: #4299e1;
}

.company-info h1 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.user-info {
    font-size: 0.8rem;
    color: #a0aec0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.currency-display {
    background: rgba(66, 153, 225, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e53e3e;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    right: 0;
    top: 70px;
    width: 280px;
    height: calc(100vh - 70px);
    background: #1a202c;
    color: white;
    overflow-y: auto;
    z-index: 999;
    border-left: 1px solid #2d3748;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #2d3748;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.username {
    font-weight: 600;
    font-size: 1rem;
}

.user-role {
    font-size: 0.8rem;
    color: #a0aec0;
}

.nav-menu {
    list-style: none;
    padding: 0;
}

.nav-category {
    padding: 1rem 1.5rem 0.5rem;
    border-bottom: none;
}

.category-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: #a0aec0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-menu li:not(.nav-category) {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    position: relative;
}

.nav-link:hover {
    background-color: #2d3748;
    color: #4299e1;
    border-right-color: #4299e1;
}

.nav-link.active {
    background-color: #2b6cb0;
    color: white;
    border-right-color: #4299e1;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
    background: #f7fafc;
    overflow-y: auto;
}

.content-section {
    display: none;
    animation: fadeInUp 0.3s ease-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section-header h2 {
    font-size: 1.8rem;
    color: #2d3748;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 0.5rem;
}

/* Dashboard Specific Styles */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.dashboard-actions {
    display: flex;
    gap: 0.5rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.stat-card.primary {
    border-left-color: #4299e1;
}

.stat-card.success {
    border-left-color: #48bb78;
}

.stat-card.warning {
    border-left-color: #ed8936;
}

.stat-card.info {
    border-left-color: #38b2ac;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stat-numbers {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.main-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.sub-number {
    font-size: 0.8rem;
    color: #718096;
}

.stat-trend {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #48bb78;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.dashboard-widget {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;
}

.widget-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
}

.widget-count {
    background: #4299e1;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.widget-count.alert {
    background: #e53e3e;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #4a5568;
}

.quick-action-btn:hover {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.5rem;
}

.quick-action-btn span {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.btn-secondary {
    background-color: #718096;
    color: white;
}

.btn-secondary:hover {
    background-color: #4a5568;
}

.btn-success {
    background-color: #48bb78;
    color: white;
}

.btn-success:hover {
    background-color: #38a169;
}

.btn-danger {
    background-color: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background-color: #c53030;
}

.btn-warning {
    background-color: #ed8936;
    color: white;
}

.btn-warning:hover {
    background-color: #dd6b20;
}

/* Settings Styles */
.settings-container {
    display: flex;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
    min-height: 600px;
}

.settings-sidebar {
    width: 250px;
    background: #f7fafc;
    border-right: 1px solid #e2e8f0;
}

.settings-menu {
    list-style: none;
    padding: 0;
}

.settings-menu li {
    margin: 0;
}

.settings-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.settings-link:hover,
.settings-link.active {
    background-color: white;
    color: #4299e1;
    border-right-color: #4299e1;
}

.settings-content {
    flex: 1;
    padding: 2rem;
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.settings-tab h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

/* Logo Upload */
.logo-upload {
    position: relative;
}

.logo-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.logo-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logo-preview:hover {
    border-color: #4299e1;
    background: #ebf8ff;
}

.logo-preview i {
    font-size: 2rem;
    color: #a0aec0;
}

.logo-preview span {
    color: #718096;
    font-weight: 500;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
}

.toggle-switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    background-color: #cbd5e0;
    border-radius: 24px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.toggle-label::before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label {
    background-color: #4299e1;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label::before {
    transform: translateX(26px);
}

/* Currency Selector */
.currency-selector {
    margin-left: 1rem;
}

.currency-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.currency-select:hover {
    background: rgba(255, 255, 255, 0.2);
}

.currency-select option {
    background: #2d3748;
    color: white;
}

/* Import/Export Styles */
.import-export-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
}

.import-export-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.export-section,
.import-section {
    padding: 1.5rem;
    border-radius: 12px;
    background: #f7fafc;
}

.export-section h3,
.import-section h3 {
    color: #2d3748;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.export-options,
.import-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.export-option,
.import-option {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.export-option:hover,
.import-option:hover {
    border-color: #4299e1;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
}

.export-option h4,
.import-option h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.export-option p,
.import-option p {
    color: #718096;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.import-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.import-controls .form-select {
    flex: 1;
}

.import-preview {
    margin-top: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.import-preview h5 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.preview-table {
    max-height: 300px;
    overflow: auto;
    margin-bottom: 1rem;
}

.import-actions {
    display: flex;
    gap: 1rem;
}

.import-templates {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #ebf8ff;
    border-radius: 8px;
    border: 1px solid #bee3f8;
}

.import-templates h4 {
    color: #2b6cb0;
    margin-bottom: 0.5rem;
}

.import-templates p {
    color: #2c5282;
    margin-bottom: 1rem;
    font-size: 0.8rem;
}

.template-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.import-history {
    margin-top: 2rem;
    padding: 2rem;
    border-top: 1px solid #e2e8f0;
}

.import-history h3 {
    color: #2d3748;
    margin-bottom: 1.5rem;
}

/* Backup Section Styles */
.backup-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.backup-section h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.backup-options,
.restore-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.backup-history {
    margin-top: 2rem;
}

.backup-history h4 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.backup-history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #e2e8f0;
}

.backup-info {
    flex: 1;
}

.backup-info .backup-name {
    font-weight: 600;
    color: #2d3748;
}

.backup-info .backup-details {
    font-size: 0.8rem;
    color: #718096;
    margin-top: 0.25rem;
}

.backup-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Table Styles for Import/Export */
.preview-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.8rem;
}

.preview-table th,
.preview-table td {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    text-align: right;
}

.preview-table th {
    background: #4299e1;
    color: white;
    font-weight: 600;
}

.preview-table tr:nth-child(even) {
    background: #f7fafc;
}

/* Responsive Design for Import/Export */
@media (max-width: 768px) {
    .import-export-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .import-controls {
        flex-direction: column;
    }

    .backup-options,
    .restore-options {
        flex-direction: column;
    }

    .template-buttons {
        flex-direction: column;
    }

    .backup-history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .backup-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-content h3 {
    font-size: 1rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.card-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    text-align: right;
}

.data-table tr:hover {
    background-color: #f8f9ff;
}

/* Recent Activities */
.recent-activities {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.recent-activities h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.3rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9ff;
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #666;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.report-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.report-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.report-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.modal-title {
    font-size: 1.5rem;
    color: #333;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
}

.close-btn:hover {
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.status-in-progress {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: scale(1.1);
}

.edit-btn {
    background-color: #007bff;
    color: white;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.view-btn {
    background-color: #28a745;
    color: white;
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 3000;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 300px;
    animation: slideDown 0.3s ease-out;
}

.notification-success {
    border-right: 4px solid #28a745;
    color: #155724;
}

.notification-error {
    border-right: 4px solid #dc3545;
    color: #721c24;
}

.notification-info {
    border-right: 4px solid #17a2b8;
    color: #0c5460;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Report Styles */
.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
}

.summary-card h4 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    opacity: 0.9;
}

.summary-amount {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.profit-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Order Item Styles */
.order-item-row {
    background: #f8f9ff;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.order-item-row:hover {
    border-color: #667eea;
}

/* Cost Summary Styles */
#cost-summary {
    font-family: 'Cairo', sans-serif;
}

#cost-summary p {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

#cost-summary hr {
    margin: 1rem 0;
    border: none;
    border-top: 2px solid #667eea;
}

/* Enhanced Table Styles */
.data-table tbody tr:nth-child(even) {
    background-color: #f8f9ff;
}

.data-table tbody tr:hover {
    background-color: #e9ecef;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Enhanced Button Styles */
.btn:active {
    transform: translateY(1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Mobile Enhancements */
@media (max-width: 768px) {
    .order-item-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-summary,
    .profit-summary {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .action-btn {
        min-width: 40px;
        min-height: 40px;
    }
}

/* Enhanced Modal Styles */
.modal-content {
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Enhanced Form Styles */
.form-input:invalid {
    border-color: #dc3545;
}

.form-input:valid {
    border-color: #28a745;
}

.form-group.required .form-label::after {
    content: " *";
    color: #dc3545;
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .btn,
    .action-buttons,
    .modal {
        display: none !important;
    }

    .main-content {
        margin-right: 0;
        padding: 0;
    }

    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .card {
        break-inside: avoid;
    }

    .report-content {
        break-inside: avoid;
    }

    @page {
        margin: 1cm;
        size: A4;
    }

    body {
        font-size: 12px;
        line-height: 1.4;
    }

    h1, h2, h3, h4 {
        break-after: avoid;
    }

    .summary-card {
        background: #f8f9ff !important;
        color: #333 !important;
        border: 1px solid #ddd;
    }
}

/* Professional Order Modal Styles */
.professional-order-modal {
    max-width: 98vw;
    width: 1600px;
    max-height: 95vh;
}

.compact-header {
    padding: 12px 20px;
}

/* Professional Order Form Styles */
.order-header-section {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.header-row-1 {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 12px;
    align-items: end;
}

.order-items-section {
    padding: 15px 20px;
}

.items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.items-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #fff;
}

/* Professional Item Row */
.professional-item-row {
    padding: 12px;
    border-bottom: 1px solid #f1f3f4;
    background: #fff;
    transition: background-color 0.2s;
}

.professional-item-row:hover {
    background: #f8f9fa;
}

.professional-item-row:last-child {
    border-bottom: none;
}

.item-description-section {
    margin-bottom: 10px;
}

.item-details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr auto;
    gap: 8px;
    align-items: end;
}

/* Compact Form Elements */
.form-group.compact {
    margin-bottom: 6px;
}

.form-label.compact {
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 3px;
    color: #495057;
    display: block;
}

.form-input.compact,
.form-select.compact {
    padding: 4px 6px;
    font-size: 12px;
    height: 28px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 100%;
}

.form-input.compact:focus,
.form-select.compact:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
    outline: none;
}

.btn.compact {
    padding: 4px 8px;
    font-size: 11px;
    height: 28px;
}

.btn-sm.compact {
    padding: 3px 6px;
    font-size: 10px;
    height: 24px;
}

/* Error Styling */
.form-input.error {
    border-color: #e53e3e;
    background-color: #fed7d7;
}

.action-group {
    display: flex;
    align-items: end;
    justify-content: center;
}

/* Responsive Design for Professional Order Modal */
@media (max-width: 1400px) {
    .professional-order-modal {
        width: 95vw;
    }

    .header-row-1 {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }

    .item-details-grid {
        grid-template-columns: 1.5fr 1fr 0.8fr 0.8fr 0.8fr 1fr 0.8fr 1fr auto;
        gap: 6px;
    }
}

@media (max-width: 1000px) {
    .header-row-1 {
        grid-template-columns: repeat(2, 1fr);
    }

    .item-details-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
    }
}

/* Order Calculations Section */
.order-calculations-section {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.calculations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.calculation-group {
    background: #fff;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.calc-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

/* Notes Section */
.notes-section {
    padding: 15px 20px;
    background: #fff;
}

.form-textarea.compact {
    height: auto;
    min-height: 50px;
    resize: vertical;
    padding: 6px 8px;
    font-size: 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

/* Cost Summary Section */
.cost-summary-section {
    padding: 15px 20px;
    background: #e6fffa;
    border: 2px solid #38b2ac;
    border-radius: 8px;
    margin: 15px 20px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    font-size: 12px;
}

.summary-item.total {
    grid-column: 1 / -1;
    background: #38b2ac;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.summary-label {
    font-weight: 600;
}

.summary-value {
    font-weight: bold;
    color: #2d3748;
}

.summary-item.total .summary-value {
    color: white;
}

/* Modal Footer */
.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-footer.compact {
    padding: 12px 20px;
}

/* Responsive adjustments for calculations */
@media (max-width: 1200px) {
    .calculations-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }
}
