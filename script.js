// Global Variables
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let users = JSON.parse(localStorage.getItem('users')) || [];
let systemSettings = JSON.parse(localStorage.getItem('systemSettings')) || getDefaultSettings();
let customers = JSON.parse(localStorage.getItem('customers')) || [];
let suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
let items = JSON.parse(localStorage.getItem('items')) || [];
let rawMaterials = JSON.parse(localStorage.getItem('rawMaterials')) || [];
let inventory = JSON.parse(localStorage.getItem('inventory')) || [];
let warehouses = JSON.parse(localStorage.getItem('warehouses')) || [];
let orders = JSON.parse(localStorage.getItem('orders')) || [];
let invoices = JSON.parse(localStorage.getItem('invoices')) || [];
let payments = JSON.parse(localStorage.getItem('payments')) || [];
let partners = JSON.parse(localStorage.getItem('partners')) || [];
let expenses = JSON.parse(localStorage.getItem('expenses')) || [];
let notifications = JSON.parse(localStorage.getItem('notifications')) || [];

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!currentUser) {
        showLoginScreen();
    } else {
        showMainApp();
    }

    // Initialize default users if empty
    if (users.length === 0) {
        initializeDefaultUsers();
    }

    // Add sample data if empty
    if (customers.length === 0) {
        addSampleData();
    }
});

// Default Settings
function getDefaultSettings() {
    return {
        companyName: 'شركة الدهان المتخصصة',
        tradeName: 'الدهان المتخصص',
        commercialRegister: '',
        taxNumber: '',
        address: '',
        phone: '0569329925',
        email: '<EMAIL>',
        logo: '',
        baseCurrency: 'SAR',
        exchangeRates: {
            USD: 3.75,
            EUR: 4.20,
            AED: 1.02,
            KWD: 12.30,
            QAR: 1.03
        },
        autoUpdateRates: false,
        timezone: 'Asia/Riyadh',
        dateFormat: 'DD/MM/YYYY',
        language: 'ar',
        lowStockThreshold: 10,
        notifications: {
            lowStock: true,
            orderDeadlines: true,
            paymentReminders: true,
            systemUpdates: true
        }
    };
}

// Initialize Default Users
function initializeDefaultUsers() {
    users = [
        {
            id: 1,
            username: 'admin',
            password: 'admin123',
            fullName: 'فارس نواف',
            email: '<EMAIL>',
            role: 'admin',
            permissions: ['all'],
            isActive: true,
            createdAt: new Date().toISOString()
        },
        {
            id: 2,
            username: 'user',
            password: 'user123',
            fullName: 'مستخدم تجريبي',
            email: '<EMAIL>',
            role: 'user',
            permissions: ['read', 'write'],
            isActive: true,
            createdAt: new Date().toISOString()
        }
    ];
    localStorage.setItem('users', JSON.stringify(users));
}

// Login System
function showLoginScreen() {
    document.getElementById('login-screen').style.display = 'flex';
    document.getElementById('main-app').style.display = 'none';
}

function showMainApp() {
    document.getElementById('login-screen').style.display = 'none';
    document.getElementById('main-app').style.display = 'flex';

    // Update UI with user info
    updateUserInterface();
    initializeNavigation();
    updateDashboard();
    loadAllTables();
}

function handleLogin(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    const user = users.find(u => u.username === username && u.password === password && u.isActive);

    if (user) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        showMainApp();
        showNotification('تم تسجيل الدخول بنجاح', 'success');
    } else {
        showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
    }
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        currentUser = null;
        localStorage.removeItem('currentUser');
        showLoginScreen();
        showNotification('تم تسجيل الخروج بنجاح', 'success');
    }
}

function updateUserInterface() {
    // Update company name and logo
    document.getElementById('company-name').textContent = systemSettings.companyName;
    document.getElementById('header-company-name').textContent = systemSettings.companyName;

    if (systemSettings.logo) {
        document.getElementById('company-logo-img').src = systemSettings.logo;
        document.getElementById('company-logo-img').style.display = 'block';
        document.getElementById('company-logo-icon').style.display = 'none';
    }

    // Update user info
    document.getElementById('current-user').textContent = currentUser.fullName;
    document.getElementById('sidebar-username').textContent = currentUser.fullName;

    // Update currency
    document.getElementById('current-currency').textContent = systemSettings.baseCurrency;
}

// Navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links and sections
            navLinks.forEach(l => l.classList.remove('active'));
            sections.forEach(s => s.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Show corresponding section
            const sectionId = this.getAttribute('data-section');
            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
                // Load section data
                loadSectionData(sectionId);
            }
        });
    });

    // Initialize settings tabs
    initializeSettingsTabs();
}

// Settings Navigation
function initializeSettingsTabs() {
    const settingsLinks = document.querySelectorAll('.settings-link');
    const settingsTabs = document.querySelectorAll('.settings-tab');

    settingsLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links and tabs
            settingsLinks.forEach(l => l.classList.remove('active'));
            settingsTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Show corresponding tab
            const tabId = this.getAttribute('data-tab') + '-settings-tab';
            const tab = document.getElementById(tabId);
            if (tab) {
                tab.classList.add('active');
            }
        });
    });
}

// Load section data
function loadSectionData(sectionId) {
    switch(sectionId) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'settings':
            loadSettingsData();
            break;
        case 'users':
            loadUsersTable();
            break;
        case 'customers':
            loadCustomersTable();
            break;
        case 'suppliers':
            loadSuppliersTable();
            break;
        case 'inventory':
            loadInventoryTable();
            break;
        case 'raw-materials':
            loadRawMaterialsTable();
            break;
        case 'items':
            loadItemsTable();
            break;
        case 'orders':
            loadOrdersTable();
            break;
        case 'invoices':
            loadInvoicesTable();
            break;
        case 'payments':
            loadPaymentsTable();
            break;
        case 'partners':
            loadPartnersTable();
            break;
        case 'expenses':
            loadExpensesTable();
            break;
    }
}

// Settings Functions
function loadSettingsData() {
    // Load company settings
    document.getElementById('company-name-input').value = systemSettings.companyName || '';
    document.getElementById('trade-name-input').value = systemSettings.tradeName || '';
    document.getElementById('commercial-register-input').value = systemSettings.commercialRegister || '';
    document.getElementById('tax-number-input').value = systemSettings.taxNumber || '';
    document.getElementById('company-address-input').value = systemSettings.address || '';
    document.getElementById('company-phone-input').value = systemSettings.phone || '';
    document.getElementById('company-email-input').value = systemSettings.email || '';

    // Load currency settings
    document.getElementById('base-currency-select').value = systemSettings.baseCurrency || 'SAR';
    document.getElementById('auto-update-rates').checked = systemSettings.autoUpdateRates || false;

    // Load system settings
    document.getElementById('timezone-select').value = systemSettings.timezone || 'Asia/Riyadh';
    document.getElementById('date-format-select').value = systemSettings.dateFormat || 'DD/MM/YYYY';
    document.getElementById('language-select').value = systemSettings.language || 'ar';
    document.getElementById('low-stock-threshold').value = systemSettings.lowStockThreshold || 10;

    // Load exchange rates
    loadExchangeRates();
}

function loadExchangeRates() {
    const container = document.getElementById('exchange-rates');
    const currencies = ['USD', 'EUR', 'AED', 'KWD', 'QAR'];

    container.innerHTML = currencies.map(currency => `
        <div class="exchange-rate-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding: 1rem; background: #f7fafc; border-radius: 8px;">
            <label class="form-label" style="margin-bottom: 0;">${currency}</label>
            <input type="number" class="form-input" style="width: 120px;"
                   name="rate_${currency}"
                   value="${systemSettings.exchangeRates[currency] || 1}"
                   step="0.01" min="0">
        </div>
    `).join('');
}

function saveCompanySettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.companyName = formData.get('companyName');
    systemSettings.tradeName = formData.get('tradeName');
    systemSettings.commercialRegister = formData.get('commercialRegister');
    systemSettings.taxNumber = formData.get('taxNumber');
    systemSettings.address = formData.get('address');
    systemSettings.phone = formData.get('phone');
    systemSettings.email = formData.get('email');

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    updateUserInterface();
    showNotification('تم حفظ إعدادات الشركة بنجاح', 'success');
}

function saveCurrencySettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.baseCurrency = formData.get('baseCurrency');
    systemSettings.autoUpdateRates = formData.get('autoUpdateRates') === 'on';

    // Save exchange rates
    const currencies = ['USD', 'EUR', 'AED', 'KWD', 'QAR'];
    currencies.forEach(currency => {
        const rate = formData.get(`rate_${currency}`);
        if (rate) {
            systemSettings.exchangeRates[currency] = parseFloat(rate);
        }
    });

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    updateUserInterface();
    showNotification('تم حفظ إعدادات العملة بنجاح', 'success');
}

function saveSystemSettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.timezone = formData.get('timezone');
    systemSettings.dateFormat = formData.get('dateFormat');
    systemSettings.language = formData.get('language');
    systemSettings.lowStockThreshold = parseInt(formData.get('lowStockThreshold'));

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    showNotification('تم حفظ إعدادات النظام بنجاح', 'success');
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            systemSettings.logo = e.target.result;
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

            // Update logo preview
            const preview = document.getElementById('logo-preview');
            preview.innerHTML = `<img src="${e.target.result}" alt="شعار الشركة" style="max-width: 100px; max-height: 100px; border-radius: 8px;">`;

            updateUserInterface();
            showNotification('تم رفع الشعار بنجاح', 'success');
        };
        reader.readAsDataURL(file);
    }
}

// Inventory Functions
function loadInventoryTable() {
    const tbody = document.getElementById('inventory-table');
    if (!tbody) return;

    tbody.innerHTML = inventory.map(item => `
        <tr>
            <td>${item.code}</td>
            <td>${item.name}</td>
            <td>${getCategoryName(item.category)}</td>
            <td>${getWarehouseName(item.warehouseId)}</td>
            <td>${item.currentQuantity}</td>
            <td>${item.minQuantity}</td>
            <td>${item.unit}</td>
            <td>${formatDate(item.lastMovement)}</td>
            <td><span class="status-badge status-${getStockStatus(item)}">${getStockStatusText(item)}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editInventoryItem(${item.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn view-btn" onclick="viewInventoryHistory(${item.id})">
                        <i class="fas fa-history"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function loadRawMaterialsTable() {
    const tbody = document.getElementById('raw-materials-table');
    if (!tbody) return;

    tbody.innerHTML = rawMaterials.map(material => `
        <tr>
            <td>${material.code}</td>
            <td>${material.name}</td>
            <td>${getCategoryName(material.category)}</td>
            <td>${getSupplierName(material.supplierId)}</td>
            <td>${material.availableQuantity}</td>
            <td>${material.unit}</td>
            <td>${formatCurrency(material.unitPrice)}</td>
            <td>${material.expiryDate ? formatDate(material.expiryDate) : 'غير محدد'}</td>
            <td><span class="status-badge status-${getMaterialStatus(material)}">${getMaterialStatusText(material)}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editRawMaterial(${material.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteRawMaterial(${material.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Helper Functions
function getCategoryName(category) {
    const categories = {
        'paint': 'دهانات',
        'thinner': 'مذيبات',
        'primer': 'برايمر',
        'varnish': 'ورنيش',
        'tools': 'أدوات',
        'accessories': 'إكسسوارات'
    };
    return categories[category] || category;
}

function getWarehouseName(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    return warehouse ? warehouse.name : 'غير محدد';
}

function getStockStatus(item) {
    if (item.currentQuantity <= 0) return 'out';
    if (item.currentQuantity <= item.minQuantity) return 'low';
    return 'available';
}

function getStockStatusText(item) {
    const status = getStockStatus(item);
    const statusMap = {
        'available': 'متوفر',
        'low': 'مخزون منخفض',
        'out': 'نفد المخزون'
    };
    return statusMap[status];
}

function getMaterialStatus(material) {
    if (material.availableQuantity <= 0) return 'out';
    if (material.expiryDate && new Date(material.expiryDate) < new Date()) return 'expired';
    if (material.availableQuantity <= material.minQuantity) return 'low';
    return 'available';
}

function getMaterialStatusText(material) {
    const status = getMaterialStatus(material);
    const statusMap = {
        'available': 'متوفر',
        'low': 'مخزون منخفض',
        'out': 'نفد المخزون',
        'expired': 'منتهي الصلاحية'
    };
    return statusMap[status];
}

// Dashboard Functions
function updateDashboard() {
    // Update basic stats
    const totalOrdersEl = document.getElementById('total-orders');
    const newOrdersEl = document.getElementById('new-orders');
    const totalSalesEl = document.getElementById('total-sales');
    const salesCurrencyEl = document.getElementById('sales-currency');
    const totalInventoryEl = document.getElementById('total-inventory');
    const lowStockCountEl = document.getElementById('low-stock-count');
    const activeCustomersEl = document.getElementById('active-customers');
    const totalCustomersEl = document.getElementById('total-customers');

    if (totalOrdersEl) totalOrdersEl.textContent = orders.length;
    if (newOrdersEl) {
        const today = new Date().toISOString().split('T')[0];
        const newOrders = orders.filter(o => o.date === today).length;
        newOrdersEl.textContent = newOrders;
    }

    if (totalSalesEl && salesCurrencyEl) {
        const totalSales = invoices.reduce((sum, invoice) => sum + invoice.total, 0);
        totalSalesEl.textContent = formatNumber(totalSales);
        salesCurrencyEl.textContent = systemSettings.baseCurrency;
    }

    if (totalInventoryEl) totalInventoryEl.textContent = inventory.length;
    if (lowStockCountEl) {
        const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low').length;
        lowStockCountEl.textContent = lowStockItems;
    }

    if (activeCustomersEl && totalCustomersEl) {
        const activeCustomers = customers.filter(c => {
            return orders.some(o => o.customerId === c.id);
        }).length;
        activeCustomersEl.textContent = activeCustomers;
        totalCustomersEl.textContent = customers.length;
    }

    // Load dashboard widgets
    loadRecentActivities();
    loadPendingOrders();
    loadLowStockAlerts();
    loadTopCustomers();
}

function refreshDashboard() {
    updateDashboard();
    showNotification('تم تحديث لوحة التحكم', 'success');
}

function exportDashboardReport() {
    const reportData = {
        date: new Date().toISOString(),
        stats: {
            totalOrders: orders.length,
            totalInvoices: invoices.length,
            totalCustomers: customers.length,
            totalSales: invoices.reduce((sum, invoice) => sum + invoice.total, 0),
            totalInventory: inventory.length,
            lowStockItems: inventory.filter(item => getStockStatus(item) === 'low').length
        },
        recentActivities: getRecentActivities(),
        pendingOrders: orders.filter(o => o.status === 'pending'),
        lowStockItems: inventory.filter(item => getStockStatus(item) === 'low')
    };

    const dataStr = JSON.stringify(reportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `dashboard-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم تصدير تقرير لوحة التحكم', 'success');
}

function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function getRecentActivities() {
    const activities = [
        ...orders.slice(-5).map(order => ({
            type: 'order',
            title: `أمر إنتاج جديد #${order.id}`,
            subtitle: `العميل: ${getCustomerName(order.customerId)}`,
            time: formatDate(order.date),
            icon: 'fas fa-clipboard-list',
            color: '#4299e1'
        })),
        ...invoices.slice(-5).map(invoice => ({
            type: 'invoice',
            title: `فاتورة جديدة #${invoice.id}`,
            subtitle: `المبلغ: ${formatCurrency(invoice.total)}`,
            time: formatDate(invoice.date),
            icon: 'fas fa-file-invoice',
            color: '#48bb78'
        })),
        ...payments.slice(-5).map(payment => ({
            type: 'payment',
            title: `${payment.type === 'receipt' ? 'مقبوض' : 'مدفوع'} جديد`,
            subtitle: `المبلغ: ${formatCurrency(payment.amount)}`,
            time: formatDate(payment.date),
            icon: 'fas fa-money-bill-wave',
            color: '#ed8936'
        }))
    ].sort((a, b) => new Date(b.time) - new Date(a.time)).slice(0, 8);

    return activities;
}

function loadRecentActivities() {
    const activitiesContainer = document.getElementById('recent-activities');
    if (!activitiesContainer) return;

    const activities = getRecentActivities();

    if (activities.length === 0) {
        activitiesContainer.innerHTML = `
            <div class="empty-state" style="text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <p>لا توجد أنشطة حديثة</p>
            </div>
        `;
        return;
    }

    activitiesContainer.innerHTML = activities.map(activity => `
        <div class="activity-item" style="display: flex; align-items: center; gap: 1rem; padding: 1rem; border-bottom: 1px solid #e2e8f0; transition: background-color 0.3s ease;">
            <div class="activity-icon" style="width: 40px; height: 40px; border-radius: 50%; background: ${activity.color}; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content" style="flex: 1;">
                <div class="activity-title" style="font-weight: 600; color: #2d3748; margin-bottom: 0.25rem;">${activity.title}</div>
                <div class="activity-subtitle" style="font-size: 0.8rem; color: #718096; margin-bottom: 0.25rem;">${activity.subtitle}</div>
                <div class="activity-time" style="font-size: 0.7rem; color: #a0aec0;">${activity.time}</div>
            </div>
        </div>
    `).join('');
}

function loadPendingOrders() {
    const container = document.getElementById('pending-orders');
    if (!container) return;

    const pendingOrders = orders.filter(o => o.status === 'pending').slice(0, 5);
    const countEl = document.getElementById('pending-orders-count');
    if (countEl) countEl.textContent = pendingOrders.length;

    if (pendingOrders.length === 0) {
        container.innerHTML = `
            <div class="empty-state" style="text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem; color: #48bb78;"></i>
                <p>لا توجد أوامر في الانتظار</p>
            </div>
        `;
        return;
    }

    container.innerHTML = pendingOrders.map(order => `
        <div class="pending-order-item" style="padding: 1rem; border-bottom: 1px solid #e2e8f0; cursor: pointer;" onclick="viewOrder(${order.id})">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div style="font-weight: 600; color: #2d3748;">أمر #${order.id}</div>
                    <div style="font-size: 0.8rem; color: #718096;">${getCustomerName(order.customerId)}</div>
                </div>
                <div style="text-align: left;">
                    <div style="font-size: 0.8rem; color: #e53e3e;">تاريخ التسليم</div>
                    <div style="font-size: 0.8rem; font-weight: 600;">${formatDate(order.deliveryDate)}</div>
                </div>
            </div>
        </div>
    `).join('');
}

function loadLowStockAlerts() {
    const container = document.getElementById('low-stock-items');
    if (!container) return;

    const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low').slice(0, 5);
    const countEl = document.getElementById('low-stock-alert-count');
    if (countEl) countEl.textContent = lowStockItems.length;

    if (lowStockItems.length === 0) {
        container.innerHTML = `
            <div class="empty-state" style="text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem; color: #48bb78;"></i>
                <p>جميع المواد متوفرة</p>
            </div>
        `;
        return;
    }

    container.innerHTML = lowStockItems.map(item => `
        <div class="low-stock-item" style="padding: 1rem; border-bottom: 1px solid #e2e8f0; background: #fef5e7;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div style="font-weight: 600; color: #2d3748;">${item.name}</div>
                    <div style="font-size: 0.8rem; color: #718096;">${item.code}</div>
                </div>
                <div style="text-align: left;">
                    <div style="font-size: 0.8rem; color: #e53e3e;">الكمية المتبقية</div>
                    <div style="font-size: 0.8rem; font-weight: 600; color: #e53e3e;">${item.currentQuantity} ${item.unit}</div>
                </div>
            </div>
        </div>
    `).join('');
}

function loadTopCustomers() {
    const container = document.getElementById('top-customers');
    if (!container) return;

    // Calculate customer totals
    const customerTotals = customers.map(customer => {
        const customerInvoices = invoices.filter(inv => inv.customerId === customer.id);
        const total = customerInvoices.reduce((sum, inv) => sum + inv.total, 0);
        return { ...customer, total, ordersCount: customerInvoices.length };
    }).sort((a, b) => b.total - a.total).slice(0, 5);

    if (customerTotals.length === 0) {
        container.innerHTML = `
            <div class="empty-state" style="text-align: center; padding: 2rem; color: #718096;">
                <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <p>لا توجد بيانات عملاء</p>
            </div>
        `;
        return;
    }

    container.innerHTML = customerTotals.map((customer, index) => `
        <div class="top-customer-item" style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
            <div class="customer-rank" style="width: 30px; height: 30px; border-radius: 50%; background: ${index < 3 ? '#4299e1' : '#e2e8f0'}; color: ${index < 3 ? 'white' : '#718096'}; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.8rem;">
                ${index + 1}
            </div>
            <div style="flex: 1;">
                <div style="font-weight: 600; color: #2d3748;">${customer.name}</div>
                <div style="font-size: 0.8rem; color: #718096;">${customer.ordersCount} طلبية</div>
            </div>
            <div style="text-align: left;">
                <div style="font-weight: 600; color: #2d3748;">${formatCurrency(customer.total)}</div>
            </div>
        </div>
    `).join('');
}

function viewAllActivities() {
    // This would open a detailed activities view
    showNotification('سيتم إضافة هذه الميزة قريباً', 'info');
}

// Customers Functions
function loadCustomersTable() {
    const tbody = document.getElementById('customers-table');
    tbody.innerHTML = customers.map(customer => `
        <tr>
            <td>${customer.id}</td>
            <td>${customer.name}</td>
            <td>${customer.phone}</td>
            <td>${customer.address}</td>
            <td>${formatCurrency(customer.balance)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editCustomer(${customer.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteCustomer(${customer.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openCustomerModal(customerId = null) {
    const customer = customerId ? customers.find(c => c.id === customerId) : null;
    const modalHtml = `
        <div class="modal" id="customer-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${customer ? 'تعديل العميل' : 'إضافة عميل جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('customer-modal')">&times;</button>
                </div>
                <form onsubmit="saveCustomer(event, ${customerId})">
                    <div class="form-group">
                        <label class="form-label">اسم العميل</label>
                        <input type="text" class="form-input" name="name" value="${customer?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-input" name="phone" value="${customer?.phone || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-textarea" name="address">${customer?.address || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الرصيد الافتتاحي</label>
                        <input type="number" class="form-input" name="balance" value="${customer?.balance || 0}" step="0.01">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('customer-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveCustomer(event, customerId) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    const customerData = {
        id: customerId || Date.now(),
        name: formData.get('name'),
        phone: formData.get('phone'),
        address: formData.get('address'),
        balance: parseFloat(formData.get('balance')) || 0
    };
    
    if (customerId) {
        const index = customers.findIndex(c => c.id === customerId);
        customers[index] = customerData;
    } else {
        customers.push(customerData);
    }
    
    localStorage.setItem('customers', JSON.stringify(customers));
    loadCustomersTable();
    updateDashboard();
    closeModal('customer-modal');
    showNotification('تم حفظ بيانات العميل بنجاح', 'success');
}

function editCustomer(customerId) {
    openCustomerModal(customerId);
}

function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        customers = customers.filter(c => c.id !== customerId);
        localStorage.setItem('customers', JSON.stringify(customers));
        loadCustomersTable();
        updateDashboard();
        showNotification('تم حذف العميل بنجاح', 'success');
    }
}

// Items Functions
function loadItemsTable() {
    const tbody = document.getElementById('items-table');
    tbody.innerHTML = items.map(item => `
        <tr>
            <td>${item.id}</td>
            <td>${item.name}</td>
            <td>${item.paintType}</td>
            <td>${formatCurrency(item.pricePerSqm)}</td>
            <td>متر مربع</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editItem(${item.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteItem(${item.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openItemModal(itemId = null) {
    const item = itemId ? items.find(i => i.id === itemId) : null;
    const modalHtml = `
        <div class="modal" id="item-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${item ? 'تعديل الصنف' : 'إضافة صنف جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('item-modal')">&times;</button>
                </div>
                <form onsubmit="saveItem(event, ${itemId})">
                    <div class="form-group">
                        <label class="form-label">اسم الصنف</label>
                        <input type="text" class="form-input" name="name" value="${item?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نوع الدهان</label>
                        <select class="form-select" name="paintType" required>
                            <option value="">اختر نوع الدهان</option>
                            <option value="دهان عادي" ${item?.paintType === 'دهان عادي' ? 'selected' : ''}>دهان عادي</option>
                            <option value="دهان لامع" ${item?.paintType === 'دهان لامع' ? 'selected' : ''}>دهان لامع</option>
                            <option value="دهان مطفي" ${item?.paintType === 'دهان مطفي' ? 'selected' : ''}>دهان مطفي</option>
                            <option value="ورنيش" ${item?.paintType === 'ورنيش' ? 'selected' : ''}>ورنيش</option>
                            <option value="بوية زيت" ${item?.paintType === 'بوية زيت' ? 'selected' : ''}>بوية زيت</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">سعر المتر المربع (ريال)</label>
                        <input type="number" class="form-input" name="pricePerSqm" value="${item?.pricePerSqm || ''}" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-textarea" name="description">${item?.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('item-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveItem(event, itemId) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    const itemData = {
        id: itemId || Date.now(),
        name: formData.get('name'),
        paintType: formData.get('paintType'),
        pricePerSqm: parseFloat(formData.get('pricePerSqm')),
        description: formData.get('description')
    };
    
    if (itemId) {
        const index = items.findIndex(i => i.id === itemId);
        items[index] = itemData;
    } else {
        items.push(itemData);
    }
    
    localStorage.setItem('items', JSON.stringify(items));
    loadItemsTable();
    closeModal('item-modal');
    showNotification('تم حفظ بيانات الصنف بنجاح', 'success');
}

function editItem(itemId) {
    openItemModal(itemId);
}

function deleteItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
        items = items.filter(i => i.id !== itemId);
        localStorage.setItem('items', JSON.stringify(items));
        loadItemsTable();
        showNotification('تم حذف الصنف بنجاح', 'success');
    }
}

// Utility Functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function loadAllTables() {
    loadCustomersTable();
    loadSuppliersTable();
    loadInventoryTable();
    loadRawMaterialsTable();
    loadItemsTable();
    loadOrdersTable();
    loadInvoicesTable();
    loadPaymentsTable();
    loadPartnersTable();
}

// Notification Functions
function openNotifications() {
    const modalHtml = `
        <div class="modal" id="notifications-modal">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3 class="modal-title">الإشعارات</h3>
                    <button class="close-btn" onclick="closeModal('notifications-modal')">&times;</button>
                </div>
                <div class="notifications-list">
                    ${generateNotifications().map(notification => `
                        <div class="notification-item" style="padding: 1rem; border-bottom: 1px solid #e2e8f0; display: flex; align-items: center; gap: 1rem;">
                            <div class="notification-icon" style="width: 40px; height: 40px; border-radius: 50%; background: ${notification.color}; display: flex; align-items: center; justify-content: center; color: white;">
                                <i class="${notification.icon}"></i>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #2d3748;">${notification.title}</div>
                                <div style="font-size: 0.8rem; color: #718096; margin-top: 0.25rem;">${notification.message}</div>
                                <div style="font-size: 0.7rem; color: #a0aec0; margin-top: 0.25rem;">${notification.time}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="form-group" style="margin-top: 1rem;">
                    <button class="btn btn-secondary" onclick="markAllNotificationsRead()">
                        <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal('notifications-modal')">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function generateNotifications() {
    const notifications = [];

    // Low stock notifications
    const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low');
    lowStockItems.forEach(item => {
        notifications.push({
            title: 'تنبيه مخزون منخفض',
            message: `المادة "${item.name}" وصلت للحد الأدنى (${item.currentQuantity} ${item.unit})`,
            time: 'منذ ساعة',
            icon: 'fas fa-exclamation-triangle',
            color: '#ed8936'
        });
    });

    // Overdue orders
    const overdueOrders = orders.filter(order => {
        return order.status === 'pending' && new Date(order.deliveryDate) < new Date();
    });
    overdueOrders.forEach(order => {
        notifications.push({
            title: 'أمر إنتاج متأخر',
            message: `أمر الإنتاج #${order.orderNumber} تجاوز تاريخ التسليم`,
            time: 'منذ يوم',
            icon: 'fas fa-clock',
            color: '#e53e3e'
        });
    });

    // Payment reminders
    const unpaidInvoices = invoices.filter(inv => inv.remaining > 0);
    if (unpaidInvoices.length > 0) {
        notifications.push({
            title: 'تذكير بالمدفوعات',
            message: `يوجد ${unpaidInvoices.length} فاتورة غير مدفوعة بالكامل`,
            time: 'منذ ساعتين',
            icon: 'fas fa-money-bill-wave',
            color: '#4299e1'
        });
    }

    return notifications.slice(0, 10);
}

function markAllNotificationsRead() {
    // Update notification count
    document.getElementById('notification-count').textContent = '0';
    closeModal('notifications-modal');
    showNotification('تم تحديد جميع الإشعارات كمقروءة', 'success');
}

function openSettings() {
    // Switch to settings section
    document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
    document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));

    const settingsLink = document.querySelector('[data-section="settings"]');
    const settingsSection = document.getElementById('settings');

    if (settingsLink && settingsSection) {
        settingsLink.classList.add('active');
        settingsSection.classList.add('active');
        loadSectionData('settings');
    }
}

// Orders Functions
function loadOrdersTable() {
    const tbody = document.getElementById('orders-table');
    tbody.innerHTML = orders.map(order => `
        <tr>
            <td>${order.id}</td>
            <td>${getCustomerName(order.customerId)}</td>
            <td>${formatDate(order.date)}</td>
            <td>${formatDate(order.deliveryDate)}</td>
            <td><span class="status-badge status-${order.status}">${getStatusText(order.status)}</span></td>
            <td>${formatCurrency(order.totalCost)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view-btn" onclick="viewOrder(${order.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" onclick="editOrder(${order.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${order.status === 'completed' ?
                        `<button class="action-btn btn-success" onclick="convertToInvoice(${order.id})">
                            <i class="fas fa-file-invoice"></i>
                        </button>` : ''}
                    <button class="action-btn delete-btn" onclick="deleteOrder(${order.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Old function removed - using enhanced version below

function createOrderItemRow(item = {}, index = 0) {
    return `
        <div class="order-item-row professional-item-row" data-index="${index}">
            <div class="item-description-section">
                <label class="form-label compact">وصف الصنف المراد دهانه</label>
                <input type="text" class="form-input compact item-description"
                       name="items[${index}][description]"
                       value="${item.description || ''}"
                       placeholder="مثال: باب خشبي، نافذة ألمنيوم، جدار خرساني..."
                       required>
            </div>

            <div class="item-details-grid">
                <div class="form-group compact">
                    <label class="form-label compact">نوع الدهان</label>
                    <select class="form-select compact item-select" name="items[${index}][itemId]" onchange="updateItemPrice(this)" required>
                        <option value="">اختر نوع الدهان</option>
                        ${items.map(i =>
                            `<option value="${i.id}" data-price="${i.pricePerSqm}" data-color="${i.colorCode || ''}" ${item.itemId === i.id ? 'selected' : ''}>${i.name} ${i.colorCode ? '- ' + i.colorCode : ''}</option>`
                        ).join('')}
                    </select>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">رقم اللون</label>
                    <input type="text" class="form-input compact color-code-input"
                           name="items[${index}][colorCode]"
                           value="${item.colorCode || ''}"
                           placeholder="RAL-9010">
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">الطول (م)</label>
                    <input type="number" class="form-input compact length-input"
                           name="items[${index}][length]"
                           value="${item.length || ''}"
                           step="0.01" min="0.01"
                           onchange="calculateItemArea(this)"
                           oninput="validateInput(this)"
                           required>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">العرض (م)</label>
                    <input type="number" class="form-input compact width-input"
                           name="items[${index}][width]"
                           value="${item.width || ''}"
                           step="0.01" min="0.01"
                           onchange="calculateItemArea(this)"
                           oninput="validateInput(this)"
                           required>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">العدد</label>
                    <input type="number" class="form-input compact quantity-input"
                           name="items[${index}][quantity]"
                           value="${item.quantity || 1}"
                           min="1" step="1"
                           onchange="calculateItemArea(this)"
                           oninput="validateInput(this)"
                           required>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">المساحة (م²)</label>
                    <input type="number" class="form-input compact area-display"
                           name="items[${index}][totalArea]"
                           value="${item.totalArea || ''}"
                           readonly>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">السعر/م²</label>
                    <input type="number" class="form-input compact price-display"
                           value="${item.pricePerSqm || ''}"
                           readonly>
                </div>

                <div class="form-group compact">
                    <label class="form-label compact">الإجمالي</label>
                    <input type="number" class="form-input compact cost-display"
                           name="items[${index}][totalCost]"
                           value="${item.totalCost || ''}"
                           readonly>
                </div>

                <div class="form-group compact action-group">
                    <label class="form-label compact">&nbsp;</label>
                    <button type="button" class="btn btn-danger btn-sm compact"
                            onclick="removeOrderItem(this)"
                            title="حذف الصنف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Sample Data
function addSampleData() {
    // Sample customers
    customers = [
        { id: 1, name: 'أحمد محمد العلي', phone: '0501234567', address: 'الرياض - حي النرجس', balance: 0, email: '<EMAIL>' },
        { id: 2, name: 'فاطمة علي السعد', phone: '0507654321', address: 'جدة - حي الروضة', balance: 500, email: '<EMAIL>' },
        { id: 3, name: 'محمد السعد الأحمد', phone: '0509876543', address: 'الدمام - حي الفيصلية', balance: -200, email: '<EMAIL>' },
        { id: 4, name: 'سارة أحمد محمد', phone: '0551234567', address: 'الخبر - حي العليا', balance: 1200, email: '<EMAIL>' },
        { id: 5, name: 'خالد عبدالله النصر', phone: '0561234567', address: 'الطائف - حي الشفا', balance: 0, email: '<EMAIL>' }
    ];

    // Sample warehouses
    warehouses = [
        { id: 1, name: 'المخزن الرئيسي', location: 'الرياض', capacity: 1000, currentStock: 750 },
        { id: 2, name: 'مخزن جدة', location: 'جدة', capacity: 500, currentStock: 300 },
        { id: 3, name: 'مخزن الدمام', location: 'الدمام', capacity: 300, currentStock: 200 }
    ];

    // Sample raw materials
    rawMaterials = [
        { id: 1, code: 'PAINT-001', name: 'دهان أكريليك أبيض', category: 'paint', supplierId: 1, availableQuantity: 50, unit: 'لتر', unitPrice: 25, minQuantity: 10, expiryDate: '2025-12-31' },
        { id: 2, code: 'PAINT-002', name: 'دهان أكريليك أسود', category: 'paint', supplierId: 1, availableQuantity: 30, unit: 'لتر', unitPrice: 25, minQuantity: 10, expiryDate: '2025-12-31' },
        { id: 3, code: 'THIN-001', name: 'مذيب عام', category: 'thinner', supplierId: 2, availableQuantity: 25, unit: 'لتر', unitPrice: 15, minQuantity: 15, expiryDate: '2024-12-31' },
        { id: 4, code: 'PRIM-001', name: 'برايمر خشب', category: 'primer', supplierId: 1, availableQuantity: 40, unit: 'لتر', unitPrice: 30, minQuantity: 10, expiryDate: '2025-06-30' },
        { id: 5, code: 'VARN-001', name: 'ورنيش لامع', category: 'varnish', supplierId: 2, availableQuantity: 8, unit: 'لتر', unitPrice: 45, minQuantity: 10, expiryDate: '2025-03-31' }
    ];

    // Sample inventory
    inventory = [
        { id: 1, code: 'INV-001', name: 'دهان أكريليك أبيض', category: 'paint', warehouseId: 1, currentQuantity: 50, minQuantity: 10, unit: 'لتر', lastMovement: '2024-01-15' },
        { id: 2, code: 'INV-002', name: 'دهان أكريليك أسود', category: 'paint', warehouseId: 1, currentQuantity: 30, minQuantity: 10, unit: 'لتر', lastMovement: '2024-01-14' },
        { id: 3, code: 'INV-003', name: 'مذيب عام', category: 'thinner', warehouseId: 2, currentQuantity: 25, minQuantity: 15, unit: 'لتر', lastMovement: '2024-01-13' },
        { id: 4, code: 'INV-004', name: 'برايمر خشب', category: 'primer', warehouseId: 1, currentQuantity: 40, minQuantity: 10, unit: 'لتر', lastMovement: '2024-01-12' },
        { id: 5, code: 'INV-005', name: 'ورنيش لامع', category: 'varnish', warehouseId: 3, currentQuantity: 8, minQuantity: 10, unit: 'لتر', lastMovement: '2024-01-11' }
    ];

    // Sample items (finished products)
    items = [
        { id: 1, code: 'PROD-001', name: 'دهان خشب عادي أبيض', paintType: 'دهان عادي', colorCode: 'RAL-9010', pricePerSqm: 25, unit: 'م²', dryingTime: '4 ساعات', coverage: '12 م²/لتر', description: 'دهان خشب عادي للأثاث' },
        { id: 2, code: 'PROD-002', name: 'ورنيش لامع شفاف', paintType: 'ورنيش', colorCode: 'CLEAR', pricePerSqm: 35, unit: 'م²', dryingTime: '6 ساعات', coverage: '10 م²/لتر', description: 'ورنيش لامع عالي الجودة' },
        { id: 3, code: 'PROD-003', name: 'بوية زيت بني', paintType: 'بوية زيت', colorCode: 'RAL-8017', pricePerSqm: 30, unit: 'م²', dryingTime: '8 ساعات', coverage: '8 م²/لتر', description: 'بوية زيت مقاومة للرطوبة' },
        { id: 4, code: 'PROD-004', name: 'لاكيه أسود لامع', paintType: 'لاكيه', colorCode: 'RAL-9005', pricePerSqm: 45, unit: 'م²', dryingTime: '2 ساعة', coverage: '15 م²/لتر', description: 'لاكيه عالي الجودة' },
        { id: 5, code: 'PROD-005', name: 'دهان مطفي رمادي', paintType: 'دهان مطفي', colorCode: 'RAL-7040', pricePerSqm: 28, unit: 'م²', dryingTime: '3 ساعات', coverage: '11 م²/لتر', description: 'دهان مطفي للديكور' }
    ];

    // Sample suppliers
    suppliers = [
        { id: 1, name: 'شركة الدهانات المتقدمة', phone: '0112345678', address: 'الرياض - المنطقة الصناعية', balance: 0, email: '<EMAIL>' },
        { id: 2, name: 'مؤسسة الألوان الحديثة', phone: '0126789012', address: 'جدة - المنطقة الصناعية', balance: -1500, email: '<EMAIL>' },
        { id: 3, name: 'شركة المواد الكيميائية', phone: '0138765432', address: 'الدمام - المنطقة الصناعية', balance: 2500, email: '<EMAIL>' }
    ];

    // Sample partners
    partners = [
        { id: 1, name: 'فارس نواف', percentage: 60, capital: 100000, profits: 0, email: '<EMAIL>', phone: '0569329925' },
        { id: 2, name: 'أحمد محمد الشريك', percentage: 25, capital: 50000, profits: 0, email: '<EMAIL>', phone: '0551234567' },
        { id: 3, name: 'سعد عبدالله المستثمر', percentage: 15, capital: 30000, profits: 0, email: '<EMAIL>', phone: '0561234567' }
    ];

    // Sample orders
    orders = [
        {
            id: 1,
            orderNumber: 'ORD-20240115-001',
            customerId: 1,
            date: '2024-01-15',
            deliveryDate: '2024-01-25',
            items: [
                { itemId: 1, colorCode: 'RAL-9010', quantity: 2, length: 200, width: 100, singleArea: 0.2, totalArea: 0.4, totalCost: 10 }
            ],
            totalArea: 0.4,
            laborRatePerSqm: 15,
            additionalLaborCost: 50,
            laborCost: 56,
            directExpenses: 20,
            indirectExpenses: 15,
            materialsCost: 10,
            profitMargin: 20,
            profitAmount: 30.2,
            totalCost: 181.2,
            currency: 'SAR',
            notes: 'طلبية عاجلة',
            status: 'pending',
            createdBy: 1
        }
    ];

    localStorage.setItem('customers', JSON.stringify(customers));
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    localStorage.setItem('rawMaterials', JSON.stringify(rawMaterials));
    localStorage.setItem('inventory', JSON.stringify(inventory));
    localStorage.setItem('items', JSON.stringify(items));
    localStorage.setItem('partners', JSON.stringify(partners));
    localStorage.setItem('orders', JSON.stringify(orders));

    // Update notification count
    updateNotificationCount();
}

// Additional Helper Functions
function updateNotificationCount() {
    const notifications = generateNotifications();
    document.getElementById('notification-count').textContent = notifications.length;
}

function toggleAutoBackup(enabled) {
    systemSettings.autoBackup = enabled;
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

    if (enabled) {
        showNotification('تم تفعيل النسخ الاحتياطي التلقائي', 'success');
        // In a real implementation, you would set up a scheduled task here
    } else {
        showNotification('تم إلغاء النسخ الاحتياطي التلقائي', 'info');
    }
}

function saveNotificationSettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.notifications = {
        lowStock: formData.get('lowStockNotifications') === 'on',
        orderDeadlines: formData.get('deliveryNotifications') === 'on',
        paymentReminders: formData.get('paymentReminders') === 'on'
    };

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    showNotification('تم حفظ إعدادات الإشعارات', 'success');
}

function saveAutoBackupSettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.autoBackup = formData.get('autoBackup') === 'on';
    systemSettings.backupFrequency = formData.get('backupFrequency');

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    showNotification('تم حفظ إعدادات النسخ الاحتياطي', 'success');
}

function saveSecuritySettings(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    systemSettings.security = {
        sessionTimeout: parseInt(formData.get('sessionTimeout')),
        auditLog: formData.get('auditLog') === 'on',
        encryptData: formData.get('encryptData') === 'on'
    };

    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    showNotification('تم حفظ إعدادات الأمان', 'success');
}

// Enhanced Order Modal with Professional Layout
function openOrderModal(orderId = null) {
    const order = orderId ? orders.find(o => o.id === orderId) : null;
    const modalHtml = `
        <div class="modal" id="order-modal">
            <div class="modal-content professional-order-modal">
                <div class="modal-header compact-header">
                    <h3 class="modal-title">${order ? 'تعديل أمر الإنتاج' : 'إضافة أمر إنتاج جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('order-modal')">&times;</button>
                </div>
                <form onsubmit="saveOrder(event, ${orderId})" id="order-form">
                    <!-- السطر الأول: المعلومات الأساسية -->
                    <div class="order-header-section">
                        <div class="header-row-1">
                            <div class="form-group compact">
                                <label class="form-label compact">رقم الأمر</label>
                                <input type="text" class="form-input compact" value="${order?.orderNumber || generateOrderNumber()}" readonly>
                            </div>
                            <div class="form-group compact">
                                <label class="form-label compact">العميل</label>
                                <select class="form-select compact" name="customerId" required>
                                    <option value="">اختر العميل</option>
                                    ${customers.map(customer =>
                                        `<option value="${customer.id}" ${order?.customerId === customer.id ? 'selected' : ''}>${customer.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="form-group compact">
                                <label class="form-label compact">تاريخ الأمر</label>
                                <input type="date" class="form-input compact" value="${order?.date ? order.date.split('T')[0] : new Date().toISOString().split('T')[0]}" readonly>
                            </div>
                            <div class="form-group compact">
                                <label class="form-label compact">تاريخ التسليم</label>
                                <input type="date" class="form-input compact" name="deliveryDate" value="${order?.deliveryDate || ''}" required>
                            </div>
                            <div class="form-group compact">
                                <label class="form-label compact">الحالة</label>
                                <select class="form-select compact" name="status">
                                    <option value="pending" ${order?.status === 'pending' ? 'selected' : ''}>قيد الانتظار</option>
                                    <option value="in_progress" ${order?.status === 'in_progress' ? 'selected' : ''}>قيد التنفيذ</option>
                                    <option value="completed" ${order?.status === 'completed' ? 'selected' : ''}>مكتمل</option>
                                </select>
                            </div>
                            <div class="form-group compact">
                                <label class="form-label compact">الأولوية</label>
                                <select class="form-select compact" name="priority">
                                    <option value="normal" ${order?.priority === 'normal' ? 'selected' : ''}>عادي</option>
                                    <option value="high" ${order?.priority === 'high' ? 'selected' : ''}>عالي</option>
                                    <option value="urgent" ${order?.priority === 'urgent' ? 'selected' : ''}>عاجل</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- السطر الثاني: جدول الأصناف -->
                    <div class="order-items-section">
                        <div class="items-header">
                            <h4 class="section-title">تفاصيل الأصناف</h4>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="addOrderItem()">
                                <i class="fas fa-plus"></i> إضافة صنف
                            </button>
                        </div>
                        <div class="items-container" id="order-items">
                            ${order ? order.items.map((item, index) => createOrderItemRow(item, index)).join('') : createOrderItemRow()}
                        </div>
                    </div>

                    <!-- السطر الثالث: التكاليف والحسابات -->
                    <div class="order-calculations-section">
                        <div class="calculations-grid">
                            <div class="calculation-group">
                                <h4 class="section-title">حساب تكلفة العمالة</h4>
                                <div class="calc-row">
                                    <div class="form-group compact">
                                        <label class="form-label compact">سعر العمالة/م² (ريال)</label>
                                        <input type="number" class="form-input compact" name="laborRatePerSqm"
                                               value="${order?.laborRatePerSqm || 15}" step="0.01"
                                               onchange="calculateOrderCost()" oninput="validateInput(this)">
                                    </div>
                                    <div class="form-group compact">
                                        <label class="form-label compact">تكلفة عمالة إضافية (ريال)</label>
                                        <input type="number" class="form-input compact" name="additionalLaborCost"
                                               value="${order?.additionalLaborCost || 0}" step="0.01"
                                               onchange="calculateOrderCost()" oninput="validateInput(this)">
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-group">
                                <h4 class="section-title">المصاريف الإضافية</h4>
                                <div class="calc-row">
                                    <div class="form-group compact">
                                        <label class="form-label compact">المصاريف المباشرة (ريال)</label>
                                        <input type="number" class="form-input compact" name="directExpenses"
                                               value="${order?.directExpenses || 0}" step="0.01"
                                               onchange="calculateOrderCost()" oninput="validateInput(this)">
                                    </div>
                                    <div class="form-group compact">
                                        <label class="form-label compact">المصاريف غير المباشرة (ريال)</label>
                                        <input type="number" class="form-input compact" name="indirectExpenses"
                                               value="${order?.indirectExpenses || 0}" step="0.01"
                                               onchange="calculateOrderCost()" oninput="validateInput(this)">
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-group">
                                <h4 class="section-title">الربح والعملة</h4>
                                <div class="calc-row">
                                    <div class="form-group compact">
                                        <label class="form-label compact">هامش الربح (%)</label>
                                        <input type="number" class="form-input compact" name="profitMargin"
                                               value="${order?.profitMargin || 20}" step="0.01" min="0" max="100"
                                               onchange="calculateOrderCost()" oninput="validateInput(this)">
                                    </div>
                                    <div class="form-group compact">
                                        <label class="form-label compact">العملة</label>
                                        <select class="form-select compact" name="currency">
                                            <option value="SAR" ${order?.currency === 'SAR' ? 'selected' : ''}>ريال سعودي</option>
                                            <option value="USD" ${order?.currency === 'USD' ? 'selected' : ''}>دولار أمريكي</option>
                                            <option value="EUR" ${order?.currency === 'EUR' ? 'selected' : ''}>يورو</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="notes-section">
                        <div class="form-group compact">
                            <label class="form-label compact">ملاحظات</label>
                            <textarea class="form-textarea compact" name="notes" rows="2"
                                      placeholder="أدخل أي ملاحظات إضافية...">${order?.notes || ''}</textarea>
                        </div>
                    </div>

                    <!-- ملخص التكلفة -->
                    <div class="cost-summary-section">
                        <h4 class="section-title">ملخص التكلفة</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">إجمالي المساحة:</span>
                                <span class="summary-value" id="total-area-display">0 م²</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">تكلفة المواد:</span>
                                <span class="summary-value" id="materials-cost">0 ريال</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">تكلفة العمالة:</span>
                                <span class="summary-value" id="labor-cost-display">0 ريال</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">المصاريف المباشرة:</span>
                                <span class="summary-value" id="direct-cost-display">0 ريال</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">المصاريف غير المباشرة:</span>
                                <span class="summary-value" id="indirect-cost-display">0 ريال</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">مبلغ الربح:</span>
                                <span class="summary-value" id="profit-amount-display">0 ريال</span>
                            </div>
                            <div class="summary-item total">
                                <span class="summary-label">إجمالي التكلفة النهائية:</span>
                                <span class="summary-value" id="total-cost-display">0 ريال</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="modal-footer compact">
                        <button type="button" class="btn btn-secondary compact" onclick="closeModal('order-modal')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary compact">
                            <i class="fas fa-save"></i> حفظ أمر الإنتاج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;

    // Add event listeners for cost calculation and validation
    setTimeout(() => {
        addCostCalculationListeners();
        addFormValidationListeners();
        calculateOrderCost();

        // Add event listeners to existing items
        const existingItems = document.querySelectorAll('.order-item-row');
        existingItems.forEach(item => addItemEventListeners(item));
    }, 100);
}

function addFormValidationListeners() {
    // Add validation listeners to form inputs
    const form = document.getElementById('order-form');
    if (form) {
        const inputs = form.querySelectorAll('input[required], select[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateRequiredField(this);
            });
        });
    }
}

function validateRequiredField(field) {
    if (!field.value.trim()) {
        field.classList.add('error');
        return false;
    } else {
        field.classList.remove('error');
        return true;
    }
}

// Test function for the enhanced order modal
function openTestOrderModal() {
    // Ensure sample data is available
    if (!window.customers || customers.length === 0) {
        addSampleData();
    }

    // Open the enhanced modal
    openOrderModal();

    // Add some test data after a short delay
    setTimeout(() => {
        fillTestData();
    }, 200);
}

function fillTestData() {
    try {
        // Fill customer
        const customerSelect = document.querySelector('select[name="customerId"]');
        if (customerSelect && customers.length > 0) {
            customerSelect.value = customers[0].id;
        }

        // Fill delivery date (7 days from now)
        const deliveryDateInput = document.querySelector('input[name="deliveryDate"]');
        if (deliveryDateInput) {
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 7);
            deliveryDateInput.value = futureDate.toISOString().split('T')[0];
        }

        // Fill first item with test data
        const firstRow = document.querySelector('.order-item-row');
        if (firstRow) {
            const descriptionInput = firstRow.querySelector('.item-description');
            const itemSelect = firstRow.querySelector('.item-select');
            const lengthInput = firstRow.querySelector('.length-input');
            const widthInput = firstRow.querySelector('.width-input');
            const quantityInput = firstRow.querySelector('.quantity-input');

            if (descriptionInput) descriptionInput.value = 'باب خشبي رئيسي';
            if (itemSelect && items.length > 0) {
                itemSelect.value = items[0].id;
                updateItemPrice(itemSelect);
            }
            if (lengthInput) lengthInput.value = '2.1';
            if (widthInput) widthInput.value = '0.9';
            if (quantityInput) quantityInput.value = '1';

            // Trigger calculation
            if (lengthInput) calculateItemArea(lengthInput);
        }

        console.log('Test data filled successfully');
    } catch (error) {
        console.error('Error filling test data:', error);
    }
}

// Order Helper Functions
function addOrderItem() {
    try {
        const container = document.getElementById('order-items');
        if (!container) {
            console.error('Order items container not found');
            return;
        }

        const index = container.children.length;
        const newItemHtml = createOrderItemRow({}, index);
        container.insertAdjacentHTML('beforeend', newItemHtml);

        // Add event listeners to the new item
        const newItem = container.lastElementChild;
        addItemEventListeners(newItem);

        showNotification('تم إضافة صنف جديد', 'success');
    } catch (error) {
        console.error('Error adding order item:', error);
        showNotification('خطأ في إضافة الصنف', 'error');
    }
}

function removeOrderItem(button) {
    try {
        const row = button.closest('.order-item-row');
        if (!row) return;

        // Confirm deletion if item has data
        const hasData = row.querySelector('.item-description').value.trim() ||
                       row.querySelector('.length-input').value ||
                       row.querySelector('.width-input').value;

        if (hasData && !confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
            return;
        }

        row.remove();
        calculateOrderCost();
        showNotification('تم حذف الصنف', 'info');
    } catch (error) {
        console.error('Error removing order item:', error);
        showNotification('خطأ في حذف الصنف', 'error');
    }
}

function addItemEventListeners(itemElement) {
    // Add event listeners for real-time validation and calculation
    const inputs = itemElement.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.type === 'number') {
            input.addEventListener('input', function() {
                validateInput(this);
                if (this.classList.contains('length-input') ||
                    this.classList.contains('width-input') ||
                    this.classList.contains('quantity-input')) {
                    calculateItemArea(this);
                }
            });
        }
    });
}

function validateOrderForm() {
    try {
        let isValid = true;
        const errors = [];

        // Validate customer selection
        const customerId = document.querySelector('select[name="customerId"]').value;
        if (!customerId) {
            errors.push('يجب اختيار العميل');
            isValid = false;
        }

        // Validate delivery date
        const deliveryDate = document.querySelector('input[name="deliveryDate"]').value;
        if (!deliveryDate) {
            errors.push('يجب تحديد تاريخ التسليم');
            isValid = false;
        } else {
            const today = new Date();
            const delivery = new Date(deliveryDate);
            if (delivery < today) {
                errors.push('تاريخ التسليم يجب أن يكون في المستقبل');
                isValid = false;
            }
        }

        // Validate order items
        const itemRows = document.querySelectorAll('.order-item-row');
        let validItems = 0;

        itemRows.forEach((row, index) => {
            const description = row.querySelector('.item-description').value.trim();
            const itemSelect = row.querySelector('.item-select').value;
            const length = parseFloat(row.querySelector('.length-input').value);
            const width = parseFloat(row.querySelector('.width-input').value);
            const quantity = parseInt(row.querySelector('.quantity-input').value);

            if (description && itemSelect && length > 0 && width > 0 && quantity > 0) {
                validItems++;
            } else if (description || itemSelect || length || width || quantity) {
                errors.push(`الصنف رقم ${index + 1}: يجب إكمال جميع البيانات المطلوبة`);
                isValid = false;
            }
        });

        if (validItems === 0) {
            errors.push('يجب إضافة صنف واحد على الأقل مع بيانات كاملة');
            isValid = false;
        }

        // Validate numeric inputs
        const numericInputs = document.querySelectorAll('input[type="number"]');
        numericInputs.forEach(input => {
            if (input.value && input.classList.contains('error')) {
                errors.push(`قيمة غير صحيحة في حقل: ${input.closest('.form-group').querySelector('.form-label').textContent}`);
                isValid = false;
            }
        });

        // Show errors if any
        if (!isValid) {
            const errorMessage = 'يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n');
            showNotification(errorMessage, 'error');
        }

        return isValid;
    } catch (error) {
        console.error('Error validating order form:', error);
        showNotification('خطأ في التحقق من صحة البيانات', 'error');
        return false;
    }
}

// Enhanced validation and calculation functions
function validateInput(input) {
    const value = parseFloat(input.value);
    const min = parseFloat(input.min) || 0;

    // Remove any existing error styling
    input.classList.remove('error');

    // Validate input
    if (isNaN(value) || value < min) {
        input.classList.add('error');
        return false;
    }

    return true;
}

function updateItemPrice(select) {
    try {
        const row = select.closest('.order-item-row');
        const selectedOption = select.options[select.selectedIndex];
        const price = parseFloat(selectedOption.dataset.price) || 0;
        const colorCode = selectedOption.dataset.color || '';

        // Update color code if available
        const colorInput = row.querySelector('.color-code-input');
        if (colorInput && colorCode) {
            colorInput.value = colorCode;
        }

        // Update price display
        const priceDisplay = row.querySelector('.price-display');
        if (priceDisplay) {
            priceDisplay.value = price.toFixed(2);
        }

        // Recalculate area and cost
        calculateItemArea(row.querySelector('.length-input'));
    } catch (error) {
        console.error('Error updating item price:', error);
        showNotification('خطأ في تحديث سعر الصنف', 'error');
    }
}

function calculateItemArea(input) {
    try {
        const row = input.closest('.order-item-row');

        // Get values with validation
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 1;
        const length = parseFloat(row.querySelector('.length-input').value) || 0;
        const width = parseFloat(row.querySelector('.width-input').value) || 0;

        // Validate inputs
        if (length <= 0 || width <= 0 || quantity <= 0) {
            row.querySelector('.area-display').value = '';
            row.querySelector('.cost-display').value = '';
            calculateOrderCost();
            return;
        }

        // Calculate area (inputs are in meters)
        const singleArea = length * width;
        const totalArea = singleArea * quantity;

        // Update area display
        row.querySelector('.area-display').value = totalArea.toFixed(4);

        // Calculate cost
        const select = row.querySelector('.item-select');
        const priceDisplay = row.querySelector('.price-display');
        const price = parseFloat(priceDisplay?.value) || parseFloat(select.options[select.selectedIndex]?.dataset.price) || 0;
        const totalCost = totalArea * price;

        // Update cost display
        row.querySelector('.cost-display').value = totalCost.toFixed(2);

        // Update total calculations
        calculateOrderCost();

    } catch (error) {
        console.error('Error calculating item area:', error);
        showNotification('خطأ في حساب المساحة', 'error');
    }
}

function addCostCalculationListeners() {
    const inputs = document.querySelectorAll('input[name="laborCost"], input[name="directExpenses"], input[name="indirectExpenses"]');
    inputs.forEach(input => {
        input.addEventListener('input', calculateOrderCost);
    });
}

function calculateOrderCost() {
    const materialsCost = Array.from(document.querySelectorAll('.cost-display'))
        .reduce((sum, input) => sum + (parseFloat(input.value) || 0), 0);

    // Calculate labor cost based on total area and labor rate
    const totalArea = Array.from(document.querySelectorAll('.area-display'))
        .reduce((sum, input) => sum + (parseFloat(input.value) || 0), 0);

    const laborRatePerSqm = parseFloat(document.querySelector('input[name="laborRatePerSqm"]')?.value) || 0;
    const additionalLaborCost = parseFloat(document.querySelector('input[name="additionalLaborCost"]')?.value) || 0;
    const laborCost = (totalArea * laborRatePerSqm) + additionalLaborCost;

    const directExpenses = parseFloat(document.querySelector('input[name="directExpenses"]')?.value) || 0;
    const indirectExpenses = parseFloat(document.querySelector('input[name="indirectExpenses"]')?.value) || 0;

    // Calculate profit margin
    const profitMarginPercent = parseFloat(document.querySelector('input[name="profitMargin"]')?.value) || 0;
    const subtotal = materialsCost + laborCost + directExpenses + indirectExpenses;
    const profitAmount = (subtotal * profitMarginPercent) / 100;
    const totalCost = subtotal + profitAmount;

    // Update display elements
    const materialsEl = document.getElementById('materials-cost');
    const laborEl = document.getElementById('labor-cost-display');
    const directEl = document.getElementById('direct-cost-display');
    const indirectEl = document.getElementById('indirect-cost-display');
    const profitEl = document.getElementById('profit-amount-display');
    const totalEl = document.getElementById('total-cost-display');
    const areaEl = document.getElementById('total-area-display');

    if (materialsEl) materialsEl.textContent = formatCurrency(materialsCost);
    if (laborEl) laborEl.textContent = formatCurrency(laborCost);
    if (directEl) directEl.textContent = formatCurrency(directExpenses);
    if (indirectEl) indirectEl.textContent = formatCurrency(indirectExpenses);
    if (profitEl) profitEl.textContent = formatCurrency(profitAmount);
    if (totalEl) totalEl.textContent = formatCurrency(totalCost);
    if (areaEl) areaEl.textContent = totalArea.toFixed(4) + ' م²';

    // Update labor cost input if it exists
    const laborInput = document.querySelector('input[name="laborCost"]');
    if (laborInput) {
        laborInput.value = laborCost.toFixed(2);
    }
}

function saveOrder(event, orderId) {
    event.preventDefault();

    try {
        // Validate form before processing
        if (!validateOrderForm()) {
            return;
        }

        const formData = new FormData(event.target);

        // Collect order items with enhanced validation
        const orderItems = [];
        const itemRows = document.querySelectorAll('.order-item-row');

        itemRows.forEach((row, index) => {
            const description = formData.get(`items[${index}][description]`)?.trim();
            const itemId = parseInt(formData.get(`items[${index}][itemId]`));
            const colorCode = formData.get(`items[${index}][colorCode]`)?.trim();
            const quantity = parseInt(formData.get(`items[${index}][quantity]`)) || 1;
            const length = parseFloat(formData.get(`items[${index}][length]`));
            const width = parseFloat(formData.get(`items[${index}][width]`));
            const totalArea = parseFloat(formData.get(`items[${index}][totalArea]`));
            const totalCost = parseFloat(formData.get(`items[${index}][totalCost]`));

            // Enhanced validation
            if (description && itemId && length > 0 && width > 0 && quantity > 0) {
                orderItems.push({
                    description,
                    itemId,
                    colorCode: colorCode || '',
                    quantity,
                    length,
                    width,
                    singleArea: length * width,
                    totalArea,
                    totalCost
                });
            }
        });

        if (orderItems.length === 0) {
            showNotification('يجب إضافة صنف واحد على الأقل مع بيانات صحيحة', 'error');
            return;
        }
    } catch (error) {
        console.error('Error processing order items:', error);
        showNotification('خطأ في معالجة بيانات الأصناف', 'error');
        return;
    }

    const materialsCost = orderItems.reduce((sum, item) => sum + item.totalCost, 0);
    const totalArea = orderItems.reduce((sum, item) => sum + item.totalArea, 0);

    // Calculate labor cost
    const laborRatePerSqm = parseFloat(formData.get('laborRatePerSqm')) || 0;
    const additionalLaborCost = parseFloat(formData.get('additionalLaborCost')) || 0;
    const laborCost = (totalArea * laborRatePerSqm) + additionalLaborCost;

    const directExpenses = parseFloat(formData.get('directExpenses')) || 0;
    const indirectExpenses = parseFloat(formData.get('indirectExpenses')) || 0;

    // Calculate profit
    const profitMargin = parseFloat(formData.get('profitMargin')) || 0;
    const subtotal = materialsCost + laborCost + directExpenses + indirectExpenses;
    const profitAmount = (subtotal * profitMargin) / 100;
    const totalCost = subtotal + profitAmount;

    const orderData = {
        id: orderId || Date.now(),
        orderNumber: orderId ? orders.find(o => o.id === orderId).orderNumber : generateOrderNumber(),
        customerId: parseInt(formData.get('customerId')),
        date: orderId ? orders.find(o => o.id === orderId).date : new Date().toISOString(),
        deliveryDate: formData.get('deliveryDate'),
        items: orderItems,
        totalArea,
        laborRatePerSqm,
        additionalLaborCost,
        laborCost,
        directExpenses,
        indirectExpenses,
        materialsCost,
        profitMargin,
        profitAmount,
        totalCost,
        currency: formData.get('currency') || systemSettings.baseCurrency,
        notes: formData.get('notes'),
        status: orderId ? orders.find(o => o.id === orderId).status : 'pending',
        createdBy: currentUser.id,
        updatedAt: new Date().toISOString()
    };

    if (orderId) {
        const index = orders.findIndex(o => o.id === orderId);
        orders[index] = orderData;
        addActivity('order_updated', `تم تحديث أمر الإنتاج #${orderData.orderNumber}`);
    } else {
        orders.push(orderData);
        addActivity('order_created', `تم إنشاء أمر إنتاج جديد #${orderData.orderNumber}`);
    }

    // Update inventory if needed
    updateInventoryFromOrder(orderData);

    localStorage.setItem('orders', JSON.stringify(orders));
    loadOrdersTable();
    updateDashboard();
    closeModal('order-modal');
    showNotification('تم حفظ أمر الإنتاج بنجاح', 'success');
}

function generateOrderNumber() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const ordersToday = orders.filter(o => o.date.startsWith(`${year}-${month}-${day}`)).length;
    return `ORD-${year}${month}${day}-${String(ordersToday + 1).padStart(3, '0')}`;
}

function addActivity(type, description) {
    const activity = {
        id: Date.now(),
        type,
        description,
        userId: currentUser.id,
        userName: currentUser.fullName,
        timestamp: new Date().toISOString()
    };

    let activities = JSON.parse(localStorage.getItem('activities')) || [];
    activities.unshift(activity);
    activities = activities.slice(0, 100); // Keep only last 100 activities

    localStorage.setItem('activities', JSON.stringify(activities));
}

function updateInventoryFromOrder(order) {
    // This function would update inventory based on order items
    // For now, we'll just log it
    console.log('Inventory update needed for order:', order.orderNumber);
}

function editOrder(orderId) {
    openOrderModal(orderId);
}

function deleteOrder(orderId) {
    if (confirm('هل أنت متأكد من حذف أمر الإنتاج؟')) {
        orders = orders.filter(o => o.id !== orderId);
        localStorage.setItem('orders', JSON.stringify(orders));
        loadOrdersTable();
        updateDashboard();
        showNotification('تم حذف أمر الإنتاج بنجاح', 'success');
    }
}

function viewOrder(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    const customer = customers.find(c => c.id === order.customerId);
    const modalHtml = `
        <div class="modal" id="view-order-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">تفاصيل أمر الإنتاج #${order.id}</h3>
                    <button class="close-btn" onclick="closeModal('view-order-modal')">&times;</button>
                </div>
                <div class="order-details">
                    <div class="form-row">
                        <div><strong>العميل:</strong> ${customer?.name || 'غير محدد'}</div>
                        <div><strong>تاريخ الأمر:</strong> ${formatDate(order.date)}</div>
                    </div>
                    <div class="form-row">
                        <div><strong>تاريخ التسليم:</strong> ${formatDate(order.deliveryDate)}</div>
                        <div><strong>الحالة:</strong> <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span></div>
                    </div>

                    <h4>أصناف الطلبية:</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>الطول (سم)</th>
                                <th>العرض (سم)</th>
                                <th>المساحة (م²)</th>
                                <th>التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${order.items.map(item => {
                                const itemData = items.find(i => i.id === item.itemId);
                                return `
                                    <tr>
                                        <td>${itemData?.name || 'غير محدد'}</td>
                                        <td>${item.length}</td>
                                        <td>${item.width}</td>
                                        <td>${item.area}</td>
                                        <td>${formatCurrency(item.cost)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="cost-breakdown" style="background: #f8f9ff; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                        <h4>تفصيل التكلفة:</h4>
                        <p>تكلفة المواد: ${formatCurrency(order.materialsCost)}</p>
                        <p>تكلفة العمالة: ${formatCurrency(order.laborCost)}</p>
                        <p>المصاريف المباشرة: ${formatCurrency(order.directExpenses)}</p>
                        <p>المصاريف غير المباشرة: ${formatCurrency(order.indirectExpenses)}</p>
                        <hr>
                        <p><strong>إجمالي التكلفة: ${formatCurrency(order.totalCost)}</strong></p>
                    </div>

                    ${order.notes ? `<div><strong>ملاحظات:</strong> ${order.notes}</div>` : ''}

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printOrder(${order.id})">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        ${order.status === 'completed' ?
                            `<button class="btn btn-success" onclick="convertToInvoice(${order.id}); closeModal('view-order-modal')">
                                <i class="fas fa-file-invoice"></i> تحويل لفاتورة
                            </button>` :
                            `<button class="btn btn-warning" onclick="markOrderCompleted(${order.id})">
                                <i class="fas fa-check"></i> تم الانتهاء
                            </button>`
                        }
                        <button class="btn btn-secondary" onclick="closeModal('view-order-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function markOrderCompleted(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (order) {
        order.status = 'completed';
        localStorage.setItem('orders', JSON.stringify(orders));
        loadOrdersTable();
        closeModal('view-order-modal');
        showNotification('تم تحديث حالة الأمر إلى مكتمل', 'success');
    }
}

// Suppliers Functions
function loadSuppliersTable() {
    const tbody = document.getElementById('suppliers-table');
    tbody.innerHTML = suppliers.map(supplier => `
        <tr>
            <td>${supplier.id}</td>
            <td>${supplier.name}</td>
            <td>${supplier.phone}</td>
            <td>${supplier.address}</td>
            <td>${formatCurrency(supplier.balance)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editSupplier(${supplier.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteSupplier(${supplier.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openSupplierModal(supplierId = null) {
    const supplier = supplierId ? suppliers.find(s => s.id === supplierId) : null;
    const modalHtml = `
        <div class="modal" id="supplier-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${supplier ? 'تعديل المورد' : 'إضافة مورد جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('supplier-modal')">&times;</button>
                </div>
                <form onsubmit="saveSupplier(event, ${supplierId})">
                    <div class="form-group">
                        <label class="form-label">اسم المورد</label>
                        <input type="text" class="form-input" name="name" value="${supplier?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-input" name="phone" value="${supplier?.phone || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-textarea" name="address">${supplier?.address || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الرصيد الافتتاحي</label>
                        <input type="number" class="form-input" name="balance" value="${supplier?.balance || 0}" step="0.01">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('supplier-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveSupplier(event, supplierId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const supplierData = {
        id: supplierId || Date.now(),
        name: formData.get('name'),
        phone: formData.get('phone'),
        address: formData.get('address'),
        balance: parseFloat(formData.get('balance')) || 0
    };

    if (supplierId) {
        const index = suppliers.findIndex(s => s.id === supplierId);
        suppliers[index] = supplierData;
    } else {
        suppliers.push(supplierData);
    }

    localStorage.setItem('suppliers', JSON.stringify(suppliers));
    loadSuppliersTable();
    closeModal('supplier-modal');
    showNotification('تم حفظ بيانات المورد بنجاح', 'success');
}

function editSupplier(supplierId) {
    openSupplierModal(supplierId);
}

function deleteSupplier(supplierId) {
    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
        suppliers = suppliers.filter(s => s.id !== supplierId);
        localStorage.setItem('suppliers', JSON.stringify(suppliers));
        loadSuppliersTable();
        showNotification('تم حذف المورد بنجاح', 'success');
    }
}

// Partners Functions
function loadPartnersTable() {
    const tbody = document.getElementById('partners-table');
    tbody.innerHTML = partners.map(partner => `
        <tr>
            <td>${partner.id}</td>
            <td>${partner.name}</td>
            <td>${partner.percentage}%</td>
            <td>${formatCurrency(partner.capital)}</td>
            <td>${formatCurrency(partner.profits)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editPartner(${partner.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deletePartner(${partner.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openPartnerModal(partnerId = null) {
    const partner = partnerId ? partners.find(p => p.id === partnerId) : null;
    const modalHtml = `
        <div class="modal" id="partner-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${partner ? 'تعديل الشريك' : 'إضافة شريك جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('partner-modal')">&times;</button>
                </div>
                <form onsubmit="savePartner(event, ${partnerId})">
                    <div class="form-group">
                        <label class="form-label">اسم الشريك</label>
                        <input type="text" class="form-input" name="name" value="${partner?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نسبة الشراكة (%)</label>
                        <input type="number" class="form-input" name="percentage" value="${partner?.percentage || ''}" min="0" max="100" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">رأس المال</label>
                        <input type="number" class="form-input" name="capital" value="${partner?.capital || 0}" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('partner-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function savePartner(event, partnerId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const partnerData = {
        id: partnerId || Date.now(),
        name: formData.get('name'),
        percentage: parseFloat(formData.get('percentage')),
        capital: parseFloat(formData.get('capital')),
        profits: partnerId ? partners.find(p => p.id === partnerId).profits : 0
    };

    if (partnerId) {
        const index = partners.findIndex(p => p.id === partnerId);
        partners[index] = partnerData;
    } else {
        partners.push(partnerData);
    }

    localStorage.setItem('partners', JSON.stringify(partners));
    loadPartnersTable();
    closeModal('partner-modal');
    showNotification('تم حفظ بيانات الشريك بنجاح', 'success');
}

function editPartner(partnerId) {
    openPartnerModal(partnerId);
}

function deletePartner(partnerId) {
    if (confirm('هل أنت متأكد من حذف هذا الشريك؟')) {
        partners = partners.filter(p => p.id !== partnerId);
        localStorage.setItem('partners', JSON.stringify(partners));
        loadPartnersTable();
        showNotification('تم حذف الشريك بنجاح', 'success');
    }
}

// Invoices Functions
function loadInvoicesTable() {
    const tbody = document.getElementById('invoices-table');
    tbody.innerHTML = invoices.map(invoice => `
        <tr>
            <td>${invoice.id}</td>
            <td>${getCustomerName(invoice.customerId)}</td>
            <td>${formatDate(invoice.date)}</td>
            <td>${formatCurrency(invoice.total)}</td>
            <td>${formatCurrency(invoice.paid)}</td>
            <td>${formatCurrency(invoice.remaining)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view-btn" onclick="viewInvoice(${invoice.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn btn-primary" onclick="printInvoice(${invoice.id})">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteInvoice(${invoice.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function convertToInvoice(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) {
        showNotification('لم يتم العثور على أمر الإنتاج', 'error');
        return;
    }

    if (order.status !== 'completed') {
        showNotification('يجب أن يكون أمر الإنتاج مكتملاً لتحويله لفاتورة', 'error');
        return;
    }

    const invoice = {
        id: Date.now(),
        orderId: order.id,
        customerId: order.customerId,
        date: new Date().toISOString().split('T')[0],
        items: order.items,
        total: order.totalCost,
        paid: 0,
        remaining: order.totalCost,
        currency: order.currency,
        notes: order.notes
    };

    invoices.push(invoice);
    localStorage.setItem('invoices', JSON.stringify(invoices));
    loadInvoicesTable();
    updateDashboard();
    showNotification('تم تحويل أمر الإنتاج إلى فاتورة بنجاح', 'success');
}

function convertOrderToInvoice() {
    const completedOrders = orders.filter(o => o.status === 'completed');
    if (completedOrders.length === 0) {
        showNotification('لا توجد أوامر إنتاج مكتملة للتحويل', 'error');
        return;
    }

    const modalHtml = `
        <div class="modal" id="convert-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">تحويل أمر إنتاج لفاتورة</h3>
                    <button class="close-btn" onclick="closeModal('convert-modal')">&times;</button>
                </div>
                <div class="form-group">
                    <label class="form-label">اختر أمر الإنتاج</label>
                    <select class="form-select" id="order-select">
                        <option value="">اختر أمر الإنتاج</option>
                        ${completedOrders.map(order =>
                            `<option value="${order.id}">أمر #${order.id} - ${getCustomerName(order.customerId)} - ${formatCurrency(order.totalCost)}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-primary" onclick="convertSelectedOrder()">
                        <i class="fas fa-exchange-alt"></i> تحويل لفاتورة
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal('convert-modal')">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function convertSelectedOrder() {
    const orderId = parseInt(document.getElementById('order-select').value);
    if (orderId) {
        convertToInvoice(orderId);
        closeModal('convert-modal');
    } else {
        showNotification('يرجى اختيار أمر الإنتاج', 'error');
    }
}

// Payments Functions
function loadPaymentsTable() {
    const tbody = document.getElementById('payments-table');
    tbody.innerHTML = payments.map(payment => `
        <tr>
            <td>${payment.id}</td>
            <td>${payment.type === 'receipt' ? 'مقبوض' : 'مدفوع'}</td>
            <td>${payment.type === 'receipt' ? getCustomerName(payment.customerId) : getSupplierName(payment.supplierId)}</td>
            <td>${formatCurrency(payment.amount)}</td>
            <td>${payment.currency}</td>
            <td>${formatDate(payment.date)}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editPayment(${payment.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deletePayment(${payment.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function openPaymentModal(paymentId = null) {
    const payment = paymentId ? payments.find(p => p.id === paymentId) : null;
    const modalHtml = `
        <div class="modal" id="payment-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${payment ? 'تعديل العملية' : 'إضافة عملية دفع جديدة'}</h3>
                    <button class="close-btn" onclick="closeModal('payment-modal')">&times;</button>
                </div>
                <form onsubmit="savePayment(event, ${paymentId})">
                    <div class="form-group">
                        <label class="form-label">نوع العملية</label>
                        <select class="form-select" name="type" onchange="togglePaymentFields(this)" required>
                            <option value="">اختر نوع العملية</option>
                            <option value="receipt" ${payment?.type === 'receipt' ? 'selected' : ''}>مقبوض من عميل</option>
                            <option value="payment" ${payment?.type === 'payment' ? 'selected' : ''}>مدفوع لمورد</option>
                        </select>
                    </div>

                    <div class="form-group" id="customer-field" style="display: ${payment?.type === 'receipt' ? 'block' : 'none'};">
                        <label class="form-label">العميل</label>
                        <select class="form-select" name="customerId">
                            <option value="">اختر العميل</option>
                            ${customers.map(customer =>
                                `<option value="${customer.id}" ${payment?.customerId === customer.id ? 'selected' : ''}>${customer.name}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="form-group" id="supplier-field" style="display: ${payment?.type === 'payment' ? 'block' : 'none'};">
                        <label class="form-label">المورد</label>
                        <select class="form-select" name="supplierId">
                            <option value="">اختر المورد</option>
                            ${suppliers.map(supplier =>
                                `<option value="${supplier.id}" ${payment?.supplierId === supplier.id ? 'selected' : ''}>${supplier.name}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">المبلغ</label>
                            <input type="number" class="form-input" name="amount" value="${payment?.amount || ''}" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العملة</label>
                            <select class="form-select" name="currency">
                                <option value="SAR" ${payment?.currency === 'SAR' ? 'selected' : ''}>ريال سعودي</option>
                                <option value="USD" ${payment?.currency === 'USD' ? 'selected' : ''}>دولار أمريكي</option>
                                <option value="EUR" ${payment?.currency === 'EUR' ? 'selected' : ''}>يورو</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">التاريخ</label>
                        <input type="date" class="form-input" name="date" value="${payment?.date || new Date().toISOString().split('T')[0]}" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-textarea" name="notes">${payment?.notes || ''}</textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('payment-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function togglePaymentFields(select) {
    const customerField = document.getElementById('customer-field');
    const supplierField = document.getElementById('supplier-field');

    if (select.value === 'receipt') {
        customerField.style.display = 'block';
        supplierField.style.display = 'none';
    } else if (select.value === 'payment') {
        customerField.style.display = 'none';
        supplierField.style.display = 'block';
    } else {
        customerField.style.display = 'none';
        supplierField.style.display = 'none';
    }
}

function savePayment(event, paymentId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const paymentData = {
        id: paymentId || Date.now(),
        type: formData.get('type'),
        customerId: formData.get('customerId') ? parseInt(formData.get('customerId')) : null,
        supplierId: formData.get('supplierId') ? parseInt(formData.get('supplierId')) : null,
        amount: parseFloat(formData.get('amount')),
        currency: formData.get('currency'),
        date: formData.get('date'),
        notes: formData.get('notes')
    };

    if (paymentId) {
        const index = payments.findIndex(p => p.id === paymentId);
        payments[index] = paymentData;
    } else {
        payments.push(paymentData);
    }

    localStorage.setItem('payments', JSON.stringify(payments));
    loadPaymentsTable();
    closeModal('payment-modal');
    showNotification('تم حفظ عملية الدفع بنجاح', 'success');
}

function editPayment(paymentId) {
    openPaymentModal(paymentId);
}

function deletePayment(paymentId) {
    if (confirm('هل أنت متأكد من حذف عملية الدفع؟')) {
        payments = payments.filter(p => p.id !== paymentId);
        localStorage.setItem('payments', JSON.stringify(payments));
        loadPaymentsTable();
        showNotification('تم حذف عملية الدفع بنجاح', 'success');
    }
}

// Helper Functions
function getCustomerName(customerId) {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
}

function getSupplierName(supplierId) {
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'غير محدد';
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'في الانتظار',
        'in-progress': 'قيد التنفيذ',
        'completed': 'مكتمل',
        'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
}

// Reports Functions
function generateSalesReport() {
    const totalSales = invoices.reduce((sum, invoice) => sum + invoice.total, 0);
    const totalPaid = invoices.reduce((sum, invoice) => sum + invoice.paid, 0);
    const totalRemaining = invoices.reduce((sum, invoice) => sum + invoice.remaining, 0);

    const reportHtml = `
        <div class="modal" id="sales-report-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير المبيعات</h3>
                    <button class="close-btn" onclick="closeModal('sales-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي الفواتير</h4>
                            <p class="summary-amount">${invoices.length}</p>
                        </div>
                        <div class="summary-card">
                            <h4>فواتير مدفوعة</h4>
                            <p class="summary-amount" style="color: #38a169;">${invoices.filter(i => i.remaining === 0).length}</p>
                        </div>
                        <div class="summary-card">
                            <h4>فواتير جزئية</h4>
                            <p class="summary-amount" style="color: #ed8936;">${invoices.filter(i => i.paid > 0 && i.remaining > 0).length}</p>
                        </div>
                        <div class="summary-card">
                            <h4>فواتير غير مدفوعة</h4>
                            <p class="summary-amount" style="color: #e53e3e;">${invoices.filter(i => i.paid === 0).length}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة الدفع:</label>
                                <select id="sales-status-filter" onchange="filterSalesReport()">
                                    <option value="all">جميع الحالات</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="partial">مدفوعة جزئياً</option>
                                    <option value="unpaid">غير مدفوعة</option>
                                </select>
                            </div>
                            <div>
                                <label>من تاريخ:</label>
                                <input type="date" id="sales-date-from" onchange="filterSalesReport()">
                            </div>
                            <div>
                                <label>إلى تاريخ:</label>
                                <input type="date" id="sales-date-to" onchange="filterSalesReport()">
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="sales-report-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoices.map(invoice => {
                                const customer = customers.find(c => c.id === invoice.customerId);
                                const status = invoice.remaining === 0 ? 'مدفوعة' : invoice.paid > 0 ? 'مدفوعة جزئياً' : 'غير مدفوعة';
                                const statusColor = invoice.remaining === 0 ? '#38a169' : invoice.paid > 0 ? '#ed8936' : '#e53e3e';

                                return `
                                    <tr>
                                        <td>${invoice.invoiceNumber || invoice.id}</td>
                                        <td>${customer ? customer.name : 'غير محدد'}</td>
                                        <td>${new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                                        <td>${formatCurrency(invoice.total)}</td>
                                        <td>${formatCurrency(invoice.paid)}</td>
                                        <td>${formatCurrency(invoice.remaining)}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${status}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('sales')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('sales')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('sales-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateCustomersReport() {
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => orders.some(o => o.customerId === c.id)).length;
    const inactiveCustomers = totalCustomers - activeCustomers;
    const customersWithBalance = customers.filter(c => c.balance > 0).length;

    const reportHtml = `
        <div class="modal" id="customers-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير العملاء</h3>
                    <button class="close-btn" onclick="closeModal('customers-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي العملاء</h4>
                            <p class="summary-amount">${totalCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>عملاء نشطين</h4>
                            <p class="summary-amount" style="color: #38a169;">${activeCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>عملاء غير نشطين</h4>
                            <p class="summary-amount" style="color: #ed8936;">${inactiveCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>عملاء لديهم أرصدة</h4>
                            <p class="summary-amount" style="color: #4299e1;">${customersWithBalance}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة العميل:</label>
                                <select id="customer-status-filter" onchange="filterCustomersReport()">
                                    <option value="all">جميع العملاء</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <label>الرصيد:</label>
                                <select id="customer-balance-filter" onchange="filterCustomersReport()">
                                    <option value="all">جميع الأرصدة</option>
                                    <option value="positive">رصيد موجب</option>
                                    <option value="zero">رصيد صفر</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="customers-report-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الطلبيات</th>
                                <th>إجمالي المشتريات</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customers.map(customer => {
                                const customerOrders = orders.filter(o => o.customerId === customer.id);
                                const customerInvoices = invoices.filter(i => i.customerId === customer.id);
                                const totalPurchases = customerInvoices.reduce((sum, inv) => sum + inv.total, 0);
                                const isActive = customerOrders.length > 0;
                                const status = isActive ? 'active' : 'inactive';
                                const statusText = isActive ? 'نشط' : 'غير نشط';
                                const statusColor = isActive ? '#38a169' : '#e53e3e';

                                return `
                                    <tr data-status="${status}" data-balance="${customer.balance > 0 ? 'positive' : 'zero'}">
                                        <td>${customer.name}</td>
                                        <td>${customer.phone}</td>
                                        <td>${customer.email || 'غير محدد'}</td>
                                        <td>${customerOrders.length}</td>
                                        <td>${formatCurrency(totalPurchases)}</td>
                                        <td>${formatCurrency(customer.balance)}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('customers')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('customers')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('customers-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateCustomerBalanceReport() {
    try {
        console.log('generateCustomerBalanceReport called');
        console.log('customers:', customers);

        // Ensure we have sample data
        if (!customers || customers.length === 0) {
            console.log('No customers found, adding sample data');
            addSampleData();
        }

        const totalCustomers = customers.length;
        const positiveBalanceCustomers = customers.filter(c => c.balance > 0).length;
        const negativeBalanceCustomers = customers.filter(c => c.balance < 0).length;
        const zeroBalanceCustomers = customers.filter(c => c.balance === 0).length;

        console.log('Report data:', {
            totalCustomers,
            positiveBalanceCustomers,
            negativeBalanceCustomers,
            zeroBalanceCustomers
        });

        if (totalCustomers === 0) {
            showNotification('لا توجد بيانات عملاء لعرضها', 'error');
            return;
        }

    const reportHtml = `
        <div class="modal" id="customer-balance-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير أرصدة العملاء</h3>
                    <button class="close-btn" onclick="closeModal('customer-balance-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي العملاء</h4>
                            <p class="summary-amount">${totalCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أرصدة موجبة</h4>
                            <p class="summary-amount" style="color: #38a169;">${positiveBalanceCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أرصدة سالبة</h4>
                            <p class="summary-amount" style="color: #e53e3e;">${negativeBalanceCustomers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أرصدة صفر</h4>
                            <p class="summary-amount" style="color: #4299e1;">${zeroBalanceCustomers}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>نوع الرصيد:</label>
                                <select id="balance-type-filter" onchange="filterCustomerBalanceReport()">
                                    <option value="all">جميع الأرصدة</option>
                                    <option value="positive">أرصدة موجبة</option>
                                    <option value="negative">أرصدة سالبة</option>
                                    <option value="zero">أرصدة صفر</option>
                                </select>
                            </div>
                            <div>
                                <label>المبلغ من:</label>
                                <input type="number" id="balance-amount-from" placeholder="0" onchange="filterCustomerBalanceReport()">
                            </div>
                            <div>
                                <label>المبلغ إلى:</label>
                                <input type="number" id="balance-amount-to" placeholder="1000" onchange="filterCustomerBalanceReport()">
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="customer-balance-report-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>الرصيد الحالي</th>
                                <th>نوع الرصيد</th>
                                <th>آخر معاملة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customers.map(customer => {
                                const balanceType = customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : 'zero';
                                const balanceTypeText = customer.balance > 0 ? 'دائن' : customer.balance < 0 ? 'مدين' : 'متوازن';
                                const balanceColor = customer.balance > 0 ? '#38a169' : customer.balance < 0 ? '#e53e3e' : '#4299e1';

                                // Find last transaction for this customer
                                const lastInvoice = invoices.filter(i => i.customerId === customer.id)
                                    .sort((a, b) => new Date(b.date) - new Date(a.date))[0];
                                const lastTransaction = lastInvoice ? new Date(lastInvoice.date).toLocaleDateString('ar-SA') : 'لا توجد معاملات';

                                const isActive = orders.some(o => o.customerId === customer.id);
                                const statusText = isActive ? 'نشط' : 'غير نشط';
                                const statusColor = isActive ? '#38a169' : '#ed8936';

                                return `
                                    <tr data-balance-type="${balanceType}" data-balance-amount="${customer.balance}">
                                        <td>${customer.name}</td>
                                        <td>${customer.phone}</td>
                                        <td>${customer.address}</td>
                                        <td style="color: ${balanceColor}; font-weight: bold;">${formatCurrency(customer.balance)}</td>
                                        <td><span style="color: ${balanceColor}; font-weight: bold;">${balanceTypeText}</span></td>
                                        <td>${lastTransaction}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('customer-balance')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('customer-balance')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('customer-balance-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    console.log('Setting modal HTML');
    const modalContainer = document.getElementById('modal-container');
    console.log('Modal container:', modalContainer);

    if (modalContainer) {
        modalContainer.innerHTML = reportHtml;
        console.log('Modal HTML set successfully');
        showNotification('تم تحميل تقرير أرصدة العملاء', 'success');
    } else {
        console.error('Modal container not found!');
        showNotification('خطأ في تحميل التقرير', 'error');
    }

    } catch (error) {
        console.error('Error in generateCustomerBalanceReport:', error);
        showNotification('حدث خطأ في تحميل تقرير أرصدة العملاء: ' + error.message, 'error');
    }
}

// Test function for customer balance report
function testCustomerBalanceReport() {
    console.log('Testing customer balance report...');

    // Simple test modal
    const testHtml = `
        <div class="modal" id="test-modal">
            <div class="modal-content" style="max-width: 500px;">
                <div class="modal-header">
                    <h3>اختبار تقرير أرصدة العملاء</h3>
                    <button class="close-btn" onclick="closeModal('test-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <p>تم تحميل التقرير بنجاح!</p>
                    <p>عدد العملاء: ${customers.length}</p>
                    <button class="btn btn-primary" onclick="generateCustomerBalanceReport()">
                        تحميل التقرير الكامل
                    </button>
                </div>
            </div>
        </div>
    `;

    const modalContainer = document.getElementById('modal-container');
    if (modalContainer) {
        modalContainer.innerHTML = testHtml;
        console.log('Test modal loaded');
    } else {
        console.error('Modal container not found!');
    }
}

function generateSuppliersReport() {
    const totalSuppliers = suppliers.length;
    const activeSuppliers = suppliers.filter(s => rawMaterials.some(m => m.supplierId === s.id)).length;
    const inactiveSuppliers = totalSuppliers - activeSuppliers;
    const suppliersWithBalance = suppliers.filter(s => s.balance > 0).length;

    const reportHtml = `
        <div class="modal" id="suppliers-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير الموردين</h3>
                    <button class="close-btn" onclick="closeModal('suppliers-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي الموردين</h4>
                            <p class="summary-amount">${totalSuppliers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>موردين نشطين</h4>
                            <p class="summary-amount" style="color: #38a169;">${activeSuppliers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>موردين غير نشطين</h4>
                            <p class="summary-amount" style="color: #ed8936;">${inactiveSuppliers}</p>
                        </div>
                        <div class="summary-card">
                            <h4>موردين لديهم أرصدة</h4>
                            <p class="summary-amount" style="color: #4299e1;">${suppliersWithBalance}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة المورد:</label>
                                <select id="supplier-status-filter" onchange="filterSuppliersReport()">
                                    <option value="all">جميع الموردين</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <label>الرصيد:</label>
                                <select id="supplier-balance-filter" onchange="filterSuppliersReport()">
                                    <option value="all">جميع الأرصدة</option>
                                    <option value="positive">رصيد موجب</option>
                                    <option value="zero">رصيد صفر</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="suppliers-report-table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>عدد المواد</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${suppliers.map(supplier => {
                                const supplierMaterialsCount = rawMaterials.filter(m => m.supplierId === supplier.id).length;
                                const isActive = supplierMaterialsCount > 0;
                                const status = isActive ? 'active' : 'inactive';
                                const statusText = isActive ? 'نشط' : 'غير نشط';
                                const statusColor = isActive ? '#38a169' : '#e53e3e';

                                return `
                                    <tr data-status="${status}" data-balance="${supplier.balance > 0 ? 'positive' : 'zero'}">
                                        <td>${supplier.name}</td>
                                        <td>${supplier.phone}</td>
                                        <td>${supplier.address}</td>
                                        <td>${supplierMaterialsCount}</td>
                                        <td>${formatCurrency(supplier.balance)}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('suppliers')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('suppliers')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('suppliers-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateProfitReport() {
    const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.total, 0);
    const totalCosts = orders.reduce((sum, order) => sum + order.totalCost, 0);
    const totalProfit = totalRevenue - totalCosts;

    const reportHtml = `
        <div class="modal" id="profit-report-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير الأرباح وتوزيعها</h3>
                    <button class="close-btn" onclick="closeModal('profit-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي الإيرادات</h4>
                            <p class="summary-amount" style="color: #38a169;">${formatCurrency(totalRevenue)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي التكاليف</h4>
                            <p class="summary-amount" style="color: #e53e3e;">${formatCurrency(totalCosts)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>صافي الربح</h4>
                            <p class="summary-amount" style="color: ${totalProfit >= 0 ? '#4299e1' : '#e53e3e'};">${formatCurrency(totalProfit)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>هامش الربح</h4>
                            <p class="summary-amount" style="color: #ed8936;">${totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(1) : 0}%</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة الشريك:</label>
                                <select id="partner-status-filter" onchange="filterProfitReport()">
                                    <option value="all">جميع الشركاء</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <label>نسبة الشراكة:</label>
                                <select id="partner-percentage-filter" onchange="filterProfitReport()">
                                    <option value="all">جميع النسب</option>
                                    <option value="high">أكثر من 25%</option>
                                    <option value="medium">10% - 25%</option>
                                    <option value="low">أقل من 10%</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="profit-report-table">
                        <thead>
                            <tr>
                                <th>اسم الشريك</th>
                                <th>رقم الهاتف</th>
                                <th>نسبة الشراكة</th>
                                <th>نصيب الربح الحالي</th>
                                <th>الأرباح المتراكمة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${partners.map(partner => {
                                const partnerProfit = (totalProfit * partner.percentage) / 100;
                                const status = partner.isActive ? 'active' : 'inactive';
                                const statusText = partner.isActive ? 'نشط' : 'غير نشط';
                                const statusColor = partner.isActive ? '#38a169' : '#e53e3e';
                                const percentageLevel = partner.percentage > 25 ? 'high' : partner.percentage >= 10 ? 'medium' : 'low';

                                return `
                                    <tr data-status="${status}" data-percentage="${percentageLevel}">
                                        <td>${partner.name}</td>
                                        <td>${partner.phone}</td>
                                        <td>${partner.percentage}%</td>
                                        <td>${formatCurrency(partnerProfit)}</td>
                                        <td>${formatCurrency(partner.profits || 0)}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-success" onclick="distributeProfits(${totalProfit})">
                            <i class="fas fa-share-alt"></i> توزيع الأرباح
                        </button>
                        <button class="btn btn-primary" onclick="printReport('profit')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-info" onclick="exportToExcel('profit')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('profit-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function distributeProfits(totalProfit) {
    if (confirm('هل أنت متأكد من توزيع الأرباح على الشركاء؟')) {
        partners.forEach(partner => {
            const partnerProfit = (totalProfit * partner.percentage) / 100;
            partner.profits += partnerProfit;
        });

        localStorage.setItem('partners', JSON.stringify(partners));
        loadPartnersTable();
        showNotification('تم توزيع الأرباح على الشركاء بنجاح', 'success');
        closeModal('profit-report-modal');
    }
}

// Additional Reports Functions
function generateInventoryReport() {
    const totalItems = inventory.length;
    const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low');
    const totalValue = inventory.reduce((sum, item) => sum + (item.currentQuantity * item.unitCost), 0);

    const reportHtml = `
        <div class="modal" id="inventory-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير المخزون</h3>
                    <button class="close-btn" onclick="closeModal('inventory-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي الأصناف</h4>
                            <p class="summary-amount">${totalItems}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أصناف منخفضة المخزون</h4>
                            <p class="summary-amount" style="color: #e53e3e;">${lowStockItems.length}</p>
                        </div>
                        <div class="summary-card">
                            <h4>قيمة المخزون الإجمالية</h4>
                            <p class="summary-amount">${formatCurrency(totalValue)}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة المخزون:</label>
                                <select id="inventory-status-filter" onchange="filterInventoryReport()">
                                    <option value="all">جميع الحالات</option>
                                    <option value="good">مخزون جيد</option>
                                    <option value="low">مخزون منخفض</option>
                                    <option value="out">نفد المخزون</option>
                                </select>
                            </div>
                            <div>
                                <label>نوع الصنف:</label>
                                <select id="inventory-type-filter" onchange="filterInventoryReport()">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="paint">دهان</option>
                                    <option value="material">مواد خام</option>
                                    <option value="tool">أدوات</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="inventory-report-table">
                        <thead>
                            <tr>
                                <th>اسم الصنف</th>
                                <th>الكمية الحالية</th>
                                <th>الوحدة</th>
                                <th>تكلفة الوحدة</th>
                                <th>القيمة الإجمالية</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${inventory.map(item => {
                                const status = getStockStatus(item);
                                const statusText = status === 'good' ? 'جيد' : status === 'low' ? 'منخفض' : 'نفد';
                                const statusColor = status === 'good' ? '#38a169' : status === 'low' ? '#ed8936' : '#e53e3e';
                                const totalValue = item.currentQuantity * item.unitCost;

                                return `
                                    <tr data-status="${status}" data-type="${item.type || 'material'}">
                                        <td>${item.name}</td>
                                        <td>${item.currentQuantity}</td>
                                        <td>${item.unit}</td>
                                        <td>${formatCurrency(item.unitCost)}</td>
                                        <td>${formatCurrency(totalValue)}</td>
                                        <td>${item.minQuantity}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('inventory')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('inventory')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('inventory-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateProductionReport() {
    const totalOrders = orders.length;
    const completedOrders = orders.filter(o => o.status === 'completed').length;
    const pendingOrders = orders.filter(o => o.status === 'pending').length;
    const inProgressOrders = orders.filter(o => o.status === 'in-progress').length;

    const reportHtml = `
        <div class="modal" id="production-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير أوامر الإنتاج</h3>
                    <button class="close-btn" onclick="closeModal('production-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي الأوامر</h4>
                            <p class="summary-amount">${totalOrders}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أوامر مكتملة</h4>
                            <p class="summary-amount" style="color: #38a169;">${completedOrders}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أوامر قيد التنفيذ</h4>
                            <p class="summary-amount" style="color: #ed8936;">${inProgressOrders}</p>
                        </div>
                        <div class="summary-card">
                            <h4>أوامر في الانتظار</h4>
                            <p class="summary-amount" style="color: #4299e1;">${pendingOrders}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>حالة الأمر:</label>
                                <select id="production-status-filter" onchange="filterProductionReport()">
                                    <option value="all">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="in-progress">قيد التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                            <div>
                                <label>من تاريخ:</label>
                                <input type="date" id="production-date-from" onchange="filterProductionReport()">
                            </div>
                            <div>
                                <label>إلى تاريخ:</label>
                                <input type="date" id="production-date-to" onchange="filterProductionReport()">
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="production-report-table">
                        <thead>
                            <tr>
                                <th>رقم الأمر</th>
                                <th>العميل</th>
                                <th>تاريخ الأمر</th>
                                <th>تاريخ التسليم</th>
                                <th>الحالة</th>
                                <th>التكلفة الإجمالية</th>
                                <th>عدد القطع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${orders.map(order => {
                                const customer = customers.find(c => c.id === order.customerId);
                                const statusText = getStatusText(order.status);
                                const statusColor = order.status === 'completed' ? '#38a169' :
                                                  order.status === 'in-progress' ? '#ed8936' :
                                                  order.status === 'pending' ? '#4299e1' : '#e53e3e';

                                return `
                                    <tr data-status="${order.status}" data-date="${order.orderDate}">
                                        <td>${order.orderNumber}</td>
                                        <td>${customer ? customer.name : 'غير محدد'}</td>
                                        <td>${new Date(order.orderDate).toLocaleDateString('ar-SA')}</td>
                                        <td>${new Date(order.deliveryDate).toLocaleDateString('ar-SA')}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                        <td>${formatCurrency(order.totalCost || 0)}</td>
                                        <td>${order.items ? order.items.length : 0}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('production')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('production')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('production-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

// Print Functions
function printReport(reportType) {
    window.print();
}

function printOrder(orderId) {
    window.print();
}

function printInvoice(invoiceId) {
    window.print();
}

function generatePaymentsReport() {
    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const paymentsThisMonth = payments.filter(p => {
        const paymentDate = new Date(p.date);
        const now = new Date();
        return paymentDate.getMonth() === now.getMonth() && paymentDate.getFullYear() === now.getFullYear();
    });
    const monthlyTotal = paymentsThisMonth.reduce((sum, payment) => sum + payment.amount, 0);

    const reportHtml = `
        <div class="modal" id="payments-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير المدفوعات</h3>
                    <button class="close-btn" onclick="closeModal('payments-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي المدفوعات</h4>
                            <p class="summary-amount">${formatCurrency(totalPayments)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>مدفوعات هذا الشهر</h4>
                            <p class="summary-amount">${formatCurrency(monthlyTotal)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>عدد المدفوعات</h4>
                            <p class="summary-amount">${payments.length}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>نوع الدفع:</label>
                                <select id="payment-type-filter" onchange="filterPaymentsReport()">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="cash">نقدي</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                            <div>
                                <label>من تاريخ:</label>
                                <input type="date" id="payment-date-from" onchange="filterPaymentsReport()">
                            </div>
                            <div>
                                <label>إلى تاريخ:</label>
                                <input type="date" id="payment-date-to" onchange="filterPaymentsReport()">
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="payments-report-table">
                        <thead>
                            <tr>
                                <th>رقم الدفعة</th>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${payments.map(payment => {
                                const invoice = invoices.find(i => i.id === payment.invoiceId);
                                const customer = invoice ? customers.find(c => c.id === invoice.customerId) : null;

                                return `
                                    <tr data-type="${payment.method}" data-date="${payment.date}">
                                        <td>${payment.id}</td>
                                        <td>${invoice ? invoice.invoiceNumber : 'غير محدد'}</td>
                                        <td>${customer ? customer.name : 'غير محدد'}</td>
                                        <td>${formatCurrency(payment.amount)}</td>
                                        <td>${payment.method === 'cash' ? 'نقدي' : payment.method === 'bank' ? 'تحويل بنكي' : 'شيك'}</td>
                                        <td>${new Date(payment.date).toLocaleDateString('ar-SA')}</td>
                                        <td>${payment.notes || '-'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('payments')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('payments')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('payments-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateExpensesReport() {
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expensesThisMonth = expenses.filter(e => {
        const expenseDate = new Date(e.date);
        const now = new Date();
        return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
    });
    const monthlyExpenses = expensesThisMonth.reduce((sum, expense) => sum + expense.amount, 0);

    const reportHtml = `
        <div class="modal" id="expenses-report-modal">
            <div class="modal-content" style="max-width: 900px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير المصروفات</h3>
                    <button class="close-btn" onclick="closeModal('expenses-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary">
                        <div class="summary-card">
                            <h4>إجمالي المصروفات</h4>
                            <p class="summary-amount">${formatCurrency(totalExpenses)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>مصروفات هذا الشهر</h4>
                            <p class="summary-amount">${formatCurrency(monthlyExpenses)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>عدد المصروفات</h4>
                            <p class="summary-amount">${expenses.length}</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>نوع المصروف:</label>
                                <select id="expense-category-filter" onchange="filterExpensesReport()">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="materials">مواد خام</option>
                                    <option value="salaries">رواتب</option>
                                    <option value="utilities">مرافق</option>
                                    <option value="maintenance">صيانة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label>من تاريخ:</label>
                                <input type="date" id="expense-date-from" onchange="filterExpensesReport()">
                            </div>
                            <div>
                                <label>إلى تاريخ:</label>
                                <input type="date" id="expense-date-to" onchange="filterExpensesReport()">
                            </div>
                        </div>
                    </div>

                    <table class="data-table" id="expenses-report-table">
                        <thead>
                            <tr>
                                <th>رقم المصروف</th>
                                <th>الوصف</th>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>المورد</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${expenses.map(expense => {
                                const supplier = suppliers.find(s => s.id === expense.supplierId);
                                const categoryText = {
                                    'materials': 'مواد خام',
                                    'salaries': 'رواتب',
                                    'utilities': 'مرافق',
                                    'maintenance': 'صيانة',
                                    'other': 'أخرى'
                                }[expense.category] || expense.category;

                                return `
                                    <tr data-category="${expense.category}" data-date="${expense.date}">
                                        <td>${expense.id}</td>
                                        <td>${expense.description}</td>
                                        <td>${categoryText}</td>
                                        <td>${formatCurrency(expense.amount)}</td>
                                        <td>${new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                                        <td>${supplier ? supplier.name : 'غير محدد'}</td>
                                        <td>${expense.notes || '-'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('expenses')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('expenses')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('expenses-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generatePartnersReport() {
    const totalProfits = partners.reduce((sum, partner) => sum + partner.profits, 0);
    const totalPercentage = partners.reduce((sum, partner) => sum + partner.percentage, 0);

    const reportHtml = `
        <div class="modal" id="partners-report-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير الشركاء</h3>
                    <button class="close-btn" onclick="closeModal('partners-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                        <div class="summary-card" style="background: #f7fafc; padding: 1rem; border-radius: 8px; text-align: center;">
                            <h4 style="color: #4a5568; margin-bottom: 0.5rem;">عدد الشركاء</h4>
                            <p class="summary-amount" style="font-size: 1.5rem; font-weight: bold; color: #2d3748;">${partners.length}</p>
                        </div>
                        <div class="summary-card" style="background: #f0fff4; padding: 1rem; border-radius: 8px; text-align: center;">
                            <h4 style="color: #4a5568; margin-bottom: 0.5rem;">الشركاء النشطين</h4>
                            <p class="summary-amount" style="font-size: 1.5rem; font-weight: bold; color: #38a169;">${partners.filter(p => p.isActive).length}</p>
                        </div>
                        <div class="summary-card" style="background: #ebf8ff; padding: 1rem; border-radius: 8px; text-align: center;">
                            <h4 style="color: #4a5568; margin-bottom: 0.5rem;">إجمالي الأرباح الموزعة</h4>
                            <p class="summary-amount" style="font-size: 1.5rem; font-weight: bold; color: #4299e1;">${formatCurrency(totalProfits)}</p>
                        </div>
                        <div class="summary-card" style="background: #fef5e7; padding: 1rem; border-radius: 8px; text-align: center;">
                            <h4 style="color: #4a5568; margin-bottom: 0.5rem;">إجمالي النسب</h4>
                            <p class="summary-amount" style="font-size: 1.5rem; font-weight: bold; color: #ed8936;">${totalPercentage}%</p>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم الشريك</th>
                                <th>رقم الهاتف</th>
                                <th>نسبة الشراكة</th>
                                <th>الأرباح المحققة</th>
                                <th>تاريخ الانضمام</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${partners.map(partner => {
                                const statusText = partner.isActive ? 'نشط' : 'غير نشط';
                                const statusColor = partner.isActive ? '#38a169' : '#e53e3e';

                                return `
                                    <tr>
                                        <td>${partner.name}</td>
                                        <td>${partner.phone}</td>
                                        <td>${partner.percentage}%</td>
                                        <td>${formatCurrency(partner.profits)}</td>
                                        <td>${new Date(partner.joinDate).toLocaleDateString('ar-SA')}</td>
                                        <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('partners')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('partners')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('partners-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

function generateFinancialReport() {
    const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.total, 0);
    const totalPaid = invoices.reduce((sum, invoice) => sum + invoice.paid, 0);
    const totalRemaining = invoices.reduce((sum, invoice) => sum + invoice.remaining, 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const netProfit = totalRevenue - totalExpenses;
    const profitMargin = totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(2) : 0;

    const reportHtml = `
        <div class="modal" id="financial-report-modal">
            <div class="modal-content" style="max-width: 1000px;">
                <div class="modal-header">
                    <h3 class="modal-title">التقرير المالي الشامل</h3>
                    <button class="close-btn" onclick="closeModal('financial-report-modal')">&times;</button>
                </div>
                <div class="report-content">
                    <div class="report-summary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                        <div class="summary-card">
                            <h4>إجمالي الإيرادات</h4>
                            <p class="summary-amount" style="color: #38a169;">${formatCurrency(totalRevenue)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي المقبوضات</h4>
                            <p class="summary-amount" style="color: #4299e1;">${formatCurrency(totalPaid)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>المبالغ المستحقة</h4>
                            <p class="summary-amount" style="color: #ed8936;">${formatCurrency(totalRemaining)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي المصروفات</h4>
                            <p class="summary-amount" style="color: #e53e3e;">${formatCurrency(totalExpenses)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>صافي الربح</h4>
                            <p class="summary-amount" style="color: ${netProfit >= 0 ? '#38a169' : '#e53e3e'};">${formatCurrency(netProfit)}</p>
                        </div>
                        <div class="summary-card">
                            <h4>هامش الربح</h4>
                            <p class="summary-amount" style="color: ${netProfit >= 0 ? '#38a169' : '#e53e3e'};">${profitMargin}%</p>
                        </div>
                    </div>

                    <div class="report-filters" style="margin: 1rem 0; padding: 1rem; background: #f7fafc; border-radius: 8px;">
                        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                            <div>
                                <label>الفترة:</label>
                                <select id="financial-period-filter" onchange="filterFinancialReport()">
                                    <option value="all">جميع الفترات</option>
                                    <option value="month">هذا الشهر</option>
                                    <option value="quarter">هذا الربع</option>
                                    <option value="year">هذا العام</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>
                            <div>
                                <label>من تاريخ:</label>
                                <input type="date" id="financial-date-from" onchange="filterFinancialReport()">
                            </div>
                            <div>
                                <label>إلى تاريخ:</label>
                                <input type="date" id="financial-date-to" onchange="filterFinancialReport()">
                            </div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;">
                        <div>
                            <h4 style="margin-bottom: 1rem; color: #2d3748;">أعلى 5 عملاء (حسب المبيعات)</h4>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>المبيعات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${getTopCustomers(5).map(customer => `
                                        <tr>
                                            <td>${customer.name}</td>
                                            <td>${formatCurrency(customer.totalSales)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <h4 style="margin-bottom: 1rem; color: #2d3748;">أعلى 5 مصروفات (حسب الفئة)</h4>
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>الفئة</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${getTopExpenseCategories(5).map(category => `
                                        <tr>
                                            <td>${category.name}</td>
                                            <td>${formatCurrency(category.total)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printReport('financial')">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel('financial')">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button class="btn btn-info" onclick="generateDetailedFinancialReport()">
                            <i class="fas fa-chart-pie"></i> تقرير مفصل
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('financial-report-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = reportHtml;
}

// Helper Functions for Reports
function getTopCustomers(limit = 5) {
    const customerSales = customers.map(customer => {
        const customerInvoices = invoices.filter(i => i.customerId === customer.id);
        const totalSales = customerInvoices.reduce((sum, invoice) => sum + invoice.total, 0);
        return {
            ...customer,
            totalSales
        };
    });

    return customerSales
        .sort((a, b) => b.totalSales - a.totalSales)
        .slice(0, limit);
}

function getTopExpenseCategories(limit = 5) {
    const categories = {};
    expenses.forEach(expense => {
        const categoryName = {
            'materials': 'مواد خام',
            'salaries': 'رواتب',
            'utilities': 'مرافق',
            'maintenance': 'صيانة',
            'other': 'أخرى'
        }[expense.category] || expense.category;

        if (!categories[categoryName]) {
            categories[categoryName] = 0;
        }
        categories[categoryName] += expense.amount;
    });

    return Object.entries(categories)
        .map(([name, total]) => ({ name, total }))
        .sort((a, b) => b.total - a.total)
        .slice(0, limit);
}

// Filter Functions for Reports
function filterInventoryReport() {
    const statusFilter = document.getElementById('inventory-status-filter').value;
    const typeFilter = document.getElementById('inventory-type-filter').value;
    const table = document.getElementById('inventory-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const type = row.getAttribute('data-type');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const typeMatch = typeFilter === 'all' || type === typeFilter;

        row.style.display = statusMatch && typeMatch ? '' : 'none';
    });
}

function filterProductionReport() {
    const statusFilter = document.getElementById('production-status-filter').value;
    const dateFrom = document.getElementById('production-date-from').value;
    const dateTo = document.getElementById('production-date-to').value;
    const table = document.getElementById('production-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const date = row.getAttribute('data-date');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        let dateMatch = true;

        if (dateFrom && date < dateFrom) dateMatch = false;
        if (dateTo && date > dateTo) dateMatch = false;

        row.style.display = statusMatch && dateMatch ? '' : 'none';
    });
}

function filterPaymentsReport() {
    const typeFilter = document.getElementById('payment-type-filter').value;
    const dateFrom = document.getElementById('payment-date-from').value;
    const dateTo = document.getElementById('payment-date-to').value;
    const table = document.getElementById('payments-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const type = row.getAttribute('data-type');
        const date = row.getAttribute('data-date');

        const typeMatch = typeFilter === 'all' || type === typeFilter;
        let dateMatch = true;

        if (dateFrom && date < dateFrom) dateMatch = false;
        if (dateTo && date > dateTo) dateMatch = false;

        row.style.display = typeMatch && dateMatch ? '' : 'none';
    });
}

function filterExpensesReport() {
    const categoryFilter = document.getElementById('expense-category-filter').value;
    const dateFrom = document.getElementById('expense-date-from').value;
    const dateTo = document.getElementById('expense-date-to').value;
    const table = document.getElementById('expenses-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const category = row.getAttribute('data-category');
        const date = row.getAttribute('data-date');

        const categoryMatch = categoryFilter === 'all' || category === categoryFilter;
        let dateMatch = true;

        if (dateFrom && date < dateFrom) dateMatch = false;
        if (dateTo && date > dateTo) dateMatch = false;

        row.style.display = categoryMatch && dateMatch ? '' : 'none';
    });
}

function filterFinancialReport() {
    const periodFilter = document.getElementById('financial-period-filter').value;
    const dateFrom = document.getElementById('financial-date-from');
    const dateTo = document.getElementById('financial-date-to');

    const now = new Date();

    switch(periodFilter) {
        case 'month':
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            dateFrom.value = monthStart.toISOString().split('T')[0];
            dateTo.value = monthEnd.toISOString().split('T')[0];
            break;
        case 'quarter':
            const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
            const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
            dateFrom.value = quarterStart.toISOString().split('T')[0];
            dateTo.value = quarterEnd.toISOString().split('T')[0];
            break;
        case 'year':
            const yearStart = new Date(now.getFullYear(), 0, 1);
            const yearEnd = new Date(now.getFullYear(), 11, 31);
            dateFrom.value = yearStart.toISOString().split('T')[0];
            dateTo.value = yearEnd.toISOString().split('T')[0];
            break;
        case 'all':
            dateFrom.value = '';
            dateTo.value = '';
            break;
    }

    // Recalculate financial data based on selected period
    if (periodFilter !== 'custom') {
        updateFinancialReportData();
    }
}

function updateFinancialReportData() {
    // This function would recalculate the financial data based on the selected period
    // For now, we'll just show a notification
    showNotification('تم تحديث البيانات المالية للفترة المحددة', 'info');
}

// Additional Filter Functions for New Reports
function filterSalesReport() {
    const statusFilter = document.getElementById('sales-status-filter').value;
    const dateFrom = document.getElementById('sales-date-from').value;
    const dateTo = document.getElementById('sales-date-to').value;
    const table = document.getElementById('sales-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const date = row.getAttribute('data-date');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        let dateMatch = true;

        if (dateFrom && date < dateFrom) dateMatch = false;
        if (dateTo && date > dateTo) dateMatch = false;

        row.style.display = statusMatch && dateMatch ? '' : 'none';
    });
}

function filterCustomersReport() {
    const statusFilter = document.getElementById('customer-status-filter').value;
    const balanceFilter = document.getElementById('customer-balance-filter').value;
    const table = document.getElementById('customers-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const balance = row.getAttribute('data-balance');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const balanceMatch = balanceFilter === 'all' || balance === balanceFilter;

        row.style.display = statusMatch && balanceMatch ? '' : 'none';
    });
}

function filterSuppliersReport() {
    const statusFilter = document.getElementById('supplier-status-filter').value;
    const balanceFilter = document.getElementById('supplier-balance-filter').value;
    const table = document.getElementById('suppliers-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const balance = row.getAttribute('data-balance');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const balanceMatch = balanceFilter === 'all' || balance === balanceFilter;

        row.style.display = statusMatch && balanceMatch ? '' : 'none';
    });
}

function filterProfitReport() {
    const statusFilter = document.getElementById('partner-status-filter').value;
    const percentageFilter = document.getElementById('partner-percentage-filter').value;
    const table = document.getElementById('profit-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const percentage = row.getAttribute('data-percentage');

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const percentageMatch = percentageFilter === 'all' || percentage === percentageFilter;

        row.style.display = statusMatch && percentageMatch ? '' : 'none';
    });
}

function filterCustomerBalanceReport() {
    const balanceTypeFilter = document.getElementById('balance-type-filter').value;
    const amountFrom = parseFloat(document.getElementById('balance-amount-from').value) || -Infinity;
    const amountTo = parseFloat(document.getElementById('balance-amount-to').value) || Infinity;
    const table = document.getElementById('customer-balance-report-table');
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const balanceType = row.getAttribute('data-balance-type');
        const balanceAmount = parseFloat(row.getAttribute('data-balance-amount'));

        const typeMatch = balanceTypeFilter === 'all' || balanceType === balanceTypeFilter;
        const amountMatch = balanceAmount >= amountFrom && balanceAmount <= amountTo;

        row.style.display = typeMatch && amountMatch ? '' : 'none';
    });
}

// Export Functions
function exportToExcel(reportType) {
    showNotification(`جاري تصدير تقرير ${getReportName(reportType)} إلى Excel...`, 'info');

    // This would implement actual Excel export functionality
    setTimeout(() => {
        showNotification(`تم تصدير تقرير ${getReportName(reportType)} بنجاح`, 'success');
    }, 1500);
}

function getReportName(reportType) {
    const names = {
        'sales': 'المبيعات',
        'customers': 'العملاء',
        'suppliers': 'الموردين',
        'profit': 'الأرباح',
        'inventory': 'المخزون',
        'production': 'أوامر الإنتاج',
        'payments': 'المدفوعات',
        'expenses': 'المصروفات',
        'partners': 'الشركاء',
        'financial': 'المالي الشامل'
    };
    return names[reportType] || reportType;
}

// Warehouse Management Functions
function openWarehouseModal(warehouseId = null) {
    const warehouse = warehouseId ? warehouses.find(w => w.id === warehouseId) : null;

    const modalHtml = `
        <div class="modal" id="warehouse-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${warehouse ? 'تعديل المخزن' : 'إضافة مخزن جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('warehouse-modal')">&times;</button>
                </div>
                <form onsubmit="saveWarehouse(event, ${warehouseId})">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">اسم المخزن</label>
                            <input type="text" name="name" class="form-input" value="${warehouse ? warehouse.name : ''}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الموقع</label>
                            <input type="text" name="location" class="form-input" value="${warehouse ? warehouse.location : ''}" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">السعة القصوى</label>
                            <input type="number" name="capacity" class="form-input" value="${warehouse ? warehouse.capacity : ''}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المخزون الحالي</label>
                            <input type="number" name="currentStock" class="form-input" value="${warehouse ? warehouse.currentStock : 0}" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-input" rows="3">${warehouse ? warehouse.notes || '' : ''}</textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('warehouse-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveWarehouse(event, warehouseId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const warehouseData = {
        id: warehouseId || Date.now(),
        name: formData.get('name'),
        location: formData.get('location'),
        capacity: parseInt(formData.get('capacity')),
        currentStock: parseInt(formData.get('currentStock')),
        notes: formData.get('notes')
    };

    if (warehouseId) {
        const index = warehouses.findIndex(w => w.id === warehouseId);
        warehouses[index] = warehouseData;
    } else {
        warehouses.push(warehouseData);
    }

    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    loadWarehousesTable();
    closeModal('warehouse-modal');
    showNotification('تم حفظ بيانات المخزن بنجاح', 'success');
}

function loadWarehousesTable() {
    const tbody = document.querySelector('#warehouses-table tbody');
    if (!tbody) return;

    tbody.innerHTML = warehouses.map(warehouse => {
        const utilizationPercentage = ((warehouse.currentStock / warehouse.capacity) * 100).toFixed(1);
        const utilizationColor = utilizationPercentage > 80 ? '#e53e3e' : utilizationPercentage > 60 ? '#ed8936' : '#38a169';

        return `
            <tr>
                <td>${warehouse.name}</td>
                <td>${warehouse.location}</td>
                <td>${warehouse.capacity}</td>
                <td>${warehouse.currentStock}</td>
                <td>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 100px; height: 8px; background: #e2e8f0; border-radius: 4px; overflow: hidden;">
                            <div style="width: ${utilizationPercentage}%; height: 100%; background: ${utilizationColor};"></div>
                        </div>
                        <span style="color: ${utilizationColor}; font-weight: bold;">${utilizationPercentage}%</span>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="editWarehouse(${warehouse.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteWarehouse(${warehouse.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="action-btn view-btn" onclick="viewWarehouseDetails(${warehouse.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function editWarehouse(warehouseId) {
    openWarehouseModal(warehouseId);
}

function deleteWarehouse(warehouseId) {
    if (confirm('هل أنت متأكد من حذف هذا المخزن؟')) {
        warehouses = warehouses.filter(w => w.id !== warehouseId);
        localStorage.setItem('warehouses', JSON.stringify(warehouses));
        loadWarehousesTable();
        showNotification('تم حذف المخزن بنجاح', 'success');
    }
}

function viewWarehouseDetails(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    if (!warehouse) return;

    const warehouseItems = inventory.filter(item => item.warehouseId === warehouseId);

    const modalHtml = `
        <div class="modal" id="warehouse-details-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">تفاصيل المخزن: ${warehouse.name}</h3>
                    <button class="close-btn" onclick="closeModal('warehouse-details-modal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="warehouse-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                        <div class="info-card">
                            <h4>الموقع</h4>
                            <p>${warehouse.location}</p>
                        </div>
                        <div class="info-card">
                            <h4>السعة القصوى</h4>
                            <p>${warehouse.capacity}</p>
                        </div>
                        <div class="info-card">
                            <h4>المخزون الحالي</h4>
                            <p>${warehouse.currentStock}</p>
                        </div>
                        <div class="info-card">
                            <h4>نسبة الاستخدام</h4>
                            <p>${((warehouse.currentStock / warehouse.capacity) * 100).toFixed(1)}%</p>
                        </div>
                    </div>

                    <h4>الأصناف في هذا المخزن</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الصنف</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${warehouseItems.map(item => `
                                <tr>
                                    <td>${item.code}</td>
                                    <td>${item.name}</td>
                                    <td>${item.currentQuantity}</td>
                                    <td>${item.unit}</td>
                                    <td><span class="status-badge status-${getStockStatus(item)}">${getStockStatusText(item)}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="closeModal('warehouse-details-modal')">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

// Raw Materials Management Functions
function openRawMaterialModal(materialId = null) {
    const material = materialId ? rawMaterials.find(m => m.id === materialId) : null;

    const modalHtml = `
        <div class="modal" id="raw-material-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${material ? 'تعديل المادة الخام' : 'إضافة مادة خام جديدة'}</h3>
                    <button class="close-btn" onclick="closeModal('raw-material-modal')">&times;</button>
                </div>
                <form onsubmit="saveRawMaterial(event, ${materialId})">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">كود المادة</label>
                            <input type="text" name="code" class="form-input" value="${material ? material.code : ''}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المادة</label>
                            <input type="text" name="name" class="form-input" value="${material ? material.name : ''}" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select name="category" class="form-input" required>
                                <option value="">اختر الفئة</option>
                                <option value="paint" ${material && material.category === 'paint' ? 'selected' : ''}>دهانات</option>
                                <option value="thinner" ${material && material.category === 'thinner' ? 'selected' : ''}>مذيبات</option>
                                <option value="primer" ${material && material.category === 'primer' ? 'selected' : ''}>برايمر</option>
                                <option value="varnish" ${material && material.category === 'varnish' ? 'selected' : ''}>ورنيش</option>
                                <option value="tools" ${material && material.category === 'tools' ? 'selected' : ''}>أدوات</option>
                                <option value="accessories" ${material && material.category === 'accessories' ? 'selected' : ''}>إكسسوارات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المورد</label>
                            <select name="supplierId" class="form-input" required>
                                <option value="">اختر المورد</option>
                                ${suppliers.map(supplier => `
                                    <option value="${supplier.id}" ${material && material.supplierId === supplier.id ? 'selected' : ''}>
                                        ${supplier.name}
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الكمية المتوفرة</label>
                            <input type="number" name="availableQuantity" class="form-input" value="${material ? material.availableQuantity : ''}" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوحدة</label>
                            <select name="unit" class="form-input" required>
                                <option value="لتر" ${material && material.unit === 'لتر' ? 'selected' : ''}>لتر</option>
                                <option value="كيلو" ${material && material.unit === 'كيلو' ? 'selected' : ''}>كيلو</option>
                                <option value="قطعة" ${material && material.unit === 'قطعة' ? 'selected' : ''}>قطعة</option>
                                <option value="متر" ${material && material.unit === 'متر' ? 'selected' : ''}>متر</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="number" name="unitPrice" class="form-input" value="${material ? material.unitPrice : ''}" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحد الأدنى</label>
                            <input type="number" name="minQuantity" class="form-input" value="${material ? material.minQuantity : ''}" step="0.01" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تاريخ انتهاء الصلاحية</label>
                        <input type="date" name="expiryDate" class="form-input" value="${material ? material.expiryDate : ''}">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('raw-material-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveRawMaterial(event, materialId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const materialData = {
        id: materialId || Date.now(),
        code: formData.get('code'),
        name: formData.get('name'),
        category: formData.get('category'),
        supplierId: parseInt(formData.get('supplierId')),
        availableQuantity: parseFloat(formData.get('availableQuantity')),
        unit: formData.get('unit'),
        unitPrice: parseFloat(formData.get('unitPrice')),
        minQuantity: parseFloat(formData.get('minQuantity')),
        expiryDate: formData.get('expiryDate')
    };

    if (materialId) {
        const index = rawMaterials.findIndex(m => m.id === materialId);
        rawMaterials[index] = materialData;
    } else {
        rawMaterials.push(materialData);
    }

    localStorage.setItem('rawMaterials', JSON.stringify(rawMaterials));
    loadRawMaterialsTable();
    closeModal('raw-material-modal');
    showNotification('تم حفظ بيانات المادة الخام بنجاح', 'success');
}

function editRawMaterial(materialId) {
    openRawMaterialModal(materialId);
}

function deleteRawMaterial(materialId) {
    if (confirm('هل أنت متأكد من حذف هذه المادة الخام؟')) {
        rawMaterials = rawMaterials.filter(m => m.id !== materialId);
        localStorage.setItem('rawMaterials', JSON.stringify(rawMaterials));
        loadRawMaterialsTable();
        showNotification('تم حذف المادة الخام بنجاح', 'success');
    }
}

// Finished Products Management Functions
function openFinishedProductModal(productId = null) {
    const product = productId ? finishedProducts.find(p => p.id === productId) : null;

    const modalHtml = `
        <div class="modal" id="finished-product-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${product ? 'تعديل المنتج النهائي' : 'إضافة منتج نهائي جديد'}</h3>
                    <button class="close-btn" onclick="closeModal('finished-product-modal')">&times;</button>
                </div>
                <form onsubmit="saveFinishedProduct(event, ${productId})">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">كود المنتج</label>
                            <input type="text" name="code" class="form-input" value="${product ? product.code : ''}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المنتج</label>
                            <input type="text" name="name" class="form-input" value="${product ? product.name : ''}" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select name="category" class="form-input" required>
                                <option value="">اختر الفئة</option>
                                <option value="furniture" ${product && product.category === 'furniture' ? 'selected' : ''}>أثاث</option>
                                <option value="doors" ${product && product.category === 'doors' ? 'selected' : ''}>أبواب</option>
                                <option value="windows" ${product && product.category === 'windows' ? 'selected' : ''}>نوافذ</option>
                                <option value="cabinets" ${product && product.category === 'cabinets' ? 'selected' : ''}>خزائن</option>
                                <option value="decorative" ${product && product.category === 'decorative' ? 'selected' : ''}>ديكورات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الكمية المنتجة</label>
                            <input type="number" name="producedQuantity" class="form-input" value="${product ? product.producedQuantity : ''}" step="1" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">تكلفة الإنتاج</label>
                            <input type="number" name="productionCost" class="form-input" value="${product ? product.productionCost : ''}" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">سعر البيع</label>
                            <input type="number" name="sellingPrice" class="form-input" value="${product ? product.sellingPrice : ''}" step="0.01" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">تاريخ الإنتاج</label>
                            <input type="date" name="productionDate" class="form-input" value="${product ? product.productionDate : ''}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-input" required>
                                <option value="in-production" ${product && product.status === 'in-production' ? 'selected' : ''}>قيد الإنتاج</option>
                                <option value="completed" ${product && product.status === 'completed' ? 'selected' : ''}>مكتمل</option>
                                <option value="sold" ${product && product.status === 'sold' ? 'selected' : ''}>مباع</option>
                                <option value="damaged" ${product && product.status === 'damaged' ? 'selected' : ''}>تالف</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">وصف المنتج</label>
                        <textarea name="description" class="form-input" rows="3">${product ? product.description || '' : ''}</textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('finished-product-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function saveFinishedProduct(event, productId) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const productData = {
        id: productId || Date.now(),
        code: formData.get('code'),
        name: formData.get('name'),
        category: formData.get('category'),
        producedQuantity: parseInt(formData.get('producedQuantity')),
        productionCost: parseFloat(formData.get('productionCost')),
        sellingPrice: parseFloat(formData.get('sellingPrice')),
        productionDate: formData.get('productionDate'),
        status: formData.get('status'),
        description: formData.get('description')
    };

    if (productId) {
        const index = finishedProducts.findIndex(p => p.id === productId);
        finishedProducts[index] = productData;
    } else {
        finishedProducts.push(productData);
    }

    localStorage.setItem('finishedProducts', JSON.stringify(finishedProducts));
    loadFinishedProductsTable();
    closeModal('finished-product-modal');
    showNotification('تم حفظ بيانات المنتج النهائي بنجاح', 'success');
}

function loadFinishedProductsTable() {
    const tbody = document.querySelector('#finished-products-table tbody');
    if (!tbody) return;

    tbody.innerHTML = finishedProducts.map(product => {
        const profit = product.sellingPrice - product.productionCost;
        const profitMargin = ((profit / product.sellingPrice) * 100).toFixed(1);
        const statusText = {
            'in-production': 'قيد الإنتاج',
            'completed': 'مكتمل',
            'sold': 'مباع',
            'damaged': 'تالف'
        }[product.status];
        const statusColor = {
            'in-production': '#ed8936',
            'completed': '#38a169',
            'sold': '#4299e1',
            'damaged': '#e53e3e'
        }[product.status];

        return `
            <tr>
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${getCategoryName(product.category)}</td>
                <td>${product.producedQuantity}</td>
                <td>${formatCurrency(product.productionCost)}</td>
                <td>${formatCurrency(product.sellingPrice)}</td>
                <td style="color: ${profit >= 0 ? '#38a169' : '#e53e3e'};">${formatCurrency(profit)} (${profitMargin}%)</td>
                <td>${new Date(product.productionDate).toLocaleDateString('ar-SA')}</td>
                <td><span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="editFinishedProduct(${product.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteFinishedProduct(${product.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function editFinishedProduct(productId) {
    openFinishedProductModal(productId);
}

function deleteFinishedProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج النهائي؟')) {
        finishedProducts = finishedProducts.filter(p => p.id !== productId);
        localStorage.setItem('finishedProducts', JSON.stringify(finishedProducts));
        loadFinishedProductsTable();
        showNotification('تم حذف المنتج النهائي بنجاح', 'success');
    }
}

// Inventory Transfer Functions
function openInventoryTransferModal() {
    const modalHtml = `
        <div class="modal" id="inventory-transfer-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">نقل مخزون</h3>
                    <button class="close-btn" onclick="closeModal('inventory-transfer-modal')">&times;</button>
                </div>
                <form onsubmit="processInventoryTransfer(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الصنف</label>
                            <select name="itemId" class="form-input" required onchange="updateAvailableQuantity()">
                                <option value="">اختر الصنف</option>
                                ${inventory.map(item => `
                                    <option value="${item.id}" data-quantity="${item.currentQuantity}" data-warehouse="${item.warehouseId}">
                                        ${item.name} (${item.currentQuantity} ${item.unit})
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الكمية المتوفرة</label>
                            <input type="text" id="available-quantity" class="form-input" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">من مخزن</label>
                            <select name="fromWarehouse" id="from-warehouse" class="form-input" required>
                                <option value="">اختر المخزن</option>
                                ${warehouses.map(warehouse => `
                                    <option value="${warehouse.id}">${warehouse.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">إلى مخزن</label>
                            <select name="toWarehouse" class="form-input" required>
                                <option value="">اختر المخزن</option>
                                ${warehouses.map(warehouse => `
                                    <option value="${warehouse.id}">${warehouse.name}</option>
                                `).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الكمية المنقولة</label>
                            <input type="number" name="transferQuantity" class="form-input" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تاريخ النقل</label>
                            <input type="date" name="transferDate" class="form-input" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-exchange-alt"></i> نقل المخزون
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('inventory-transfer-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function updateAvailableQuantity() {
    const itemSelect = document.querySelector('select[name="itemId"]');
    const availableQuantityInput = document.getElementById('available-quantity');
    const fromWarehouseSelect = document.getElementById('from-warehouse');

    if (itemSelect.value) {
        const selectedOption = itemSelect.options[itemSelect.selectedIndex];
        const quantity = selectedOption.getAttribute('data-quantity');
        const warehouseId = selectedOption.getAttribute('data-warehouse');

        availableQuantityInput.value = quantity;
        fromWarehouseSelect.value = warehouseId;
    } else {
        availableQuantityInput.value = '';
        fromWarehouseSelect.value = '';
    }
}

function processInventoryTransfer(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const itemId = parseInt(formData.get('itemId'));
    const fromWarehouseId = parseInt(formData.get('fromWarehouse'));
    const toWarehouseId = parseInt(formData.get('toWarehouse'));
    const transferQuantity = parseFloat(formData.get('transferQuantity'));

    if (fromWarehouseId === toWarehouseId) {
        showNotification('لا يمكن نقل المخزون إلى نفس المخزن', 'error');
        return;
    }

    const item = inventory.find(i => i.id === itemId);
    if (!item) {
        showNotification('لم يتم العثور على الصنف', 'error');
        return;
    }

    if (transferQuantity > item.currentQuantity) {
        showNotification('الكمية المطلوبة أكبر من المتوفر', 'error');
        return;
    }

    // Update current item quantity
    item.currentQuantity -= transferQuantity;

    // Check if item exists in destination warehouse
    const existingItemInDestination = inventory.find(i =>
        i.name === item.name && i.warehouseId === toWarehouseId
    );

    if (existingItemInDestination) {
        existingItemInDestination.currentQuantity += transferQuantity;
    } else {
        // Create new item in destination warehouse
        const newItem = {
            ...item,
            id: Date.now(),
            warehouseId: toWarehouseId,
            currentQuantity: transferQuantity
        };
        inventory.push(newItem);
    }

    // Record transfer activity
    addActivity('inventory_transfer',
        `تم نقل ${transferQuantity} ${item.unit} من ${item.name} من ${getWarehouseName(fromWarehouseId)} إلى ${getWarehouseName(toWarehouseId)}`
    );

    localStorage.setItem('inventory', JSON.stringify(inventory));
    loadInventoryTable();
    closeModal('inventory-transfer-modal');
    showNotification('تم نقل المخزون بنجاح', 'success');
}

// Stock Adjustment Functions
function openStockAdjustmentModal() {
    const modalHtml = `
        <div class="modal" id="stock-adjustment-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">تعديل مخزون</h3>
                    <button class="close-btn" onclick="closeModal('stock-adjustment-modal')">&times;</button>
                </div>
                <form onsubmit="processStockAdjustment(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">الصنف</label>
                            <select name="itemId" class="form-input" required onchange="updateCurrentStock()">
                                <option value="">اختر الصنف</option>
                                ${inventory.map(item => `
                                    <option value="${item.id}" data-quantity="${item.currentQuantity}">
                                        ${item.name} - ${getWarehouseName(item.warehouseId)}
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المخزون الحالي</label>
                            <input type="text" id="current-stock" class="form-input" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">نوع التعديل</label>
                            <select name="adjustmentType" class="form-input" required>
                                <option value="">اختر نوع التعديل</option>
                                <option value="increase">زيادة</option>
                                <option value="decrease">نقص</option>
                                <option value="set">تحديد كمية جديدة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الكمية</label>
                            <input type="number" name="adjustmentQuantity" class="form-input" step="0.01" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">سبب التعديل</label>
                            <select name="reason" class="form-input" required>
                                <option value="">اختر السبب</option>
                                <option value="damage">تلف</option>
                                <option value="loss">فقدان</option>
                                <option value="found">عثور على كمية إضافية</option>
                                <option value="correction">تصحيح خطأ</option>
                                <option value="expiry">انتهاء صلاحية</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تاريخ التعديل</label>
                            <input type="date" name="adjustmentDate" class="form-input" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل المخزون
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('stock-adjustment-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function updateCurrentStock() {
    const itemSelect = document.querySelector('select[name="itemId"]');
    const currentStockInput = document.getElementById('current-stock');

    if (itemSelect.value) {
        const selectedOption = itemSelect.options[itemSelect.selectedIndex];
        const quantity = selectedOption.getAttribute('data-quantity');
        currentStockInput.value = quantity;
    } else {
        currentStockInput.value = '';
    }
}

function processStockAdjustment(event) {
    event.preventDefault();
    const formData = new FormData(event.target);

    const itemId = parseInt(formData.get('itemId'));
    const adjustmentType = formData.get('adjustmentType');
    const adjustmentQuantity = parseFloat(formData.get('adjustmentQuantity'));
    const reason = formData.get('reason');
    const notes = formData.get('notes');

    const item = inventory.find(i => i.id === itemId);
    if (!item) {
        showNotification('لم يتم العثور على الصنف', 'error');
        return;
    }

    const oldQuantity = item.currentQuantity;
    let newQuantity;

    switch (adjustmentType) {
        case 'increase':
            newQuantity = oldQuantity + adjustmentQuantity;
            break;
        case 'decrease':
            newQuantity = Math.max(0, oldQuantity - adjustmentQuantity);
            break;
        case 'set':
            newQuantity = adjustmentQuantity;
            break;
        default:
            showNotification('نوع التعديل غير صحيح', 'error');
            return;
    }

    item.currentQuantity = newQuantity;
    item.lastMovement = new Date().toISOString().split('T')[0];

    // Record adjustment activity
    const reasonText = {
        'damage': 'تلف',
        'loss': 'فقدان',
        'found': 'عثور على كمية إضافية',
        'correction': 'تصحيح خطأ',
        'expiry': 'انتهاء صلاحية',
        'other': 'أخرى'
    }[reason];

    addActivity('stock_adjustment',
        `تم تعديل مخزون ${item.name} من ${oldQuantity} إلى ${newQuantity} ${item.unit} - السبب: ${reasonText}`
    );

    localStorage.setItem('inventory', JSON.stringify(inventory));
    loadInventoryTable();
    closeModal('stock-adjustment-modal');
    showNotification('تم تعديل المخزون بنجاح', 'success');
}

function viewInvoice(invoiceId) {
    const invoice = invoices.find(i => i.id === invoiceId);
    if (!invoice) return;

    const customer = customers.find(c => c.id === invoice.customerId);
    const modalHtml = `
        <div class="modal" id="view-invoice-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">فاتورة رقم #${invoice.id}</h3>
                    <button class="close-btn" onclick="closeModal('view-invoice-modal')">&times;</button>
                </div>
                <div class="invoice-details">
                    <div class="invoice-header" style="text-align: center; margin-bottom: 2rem; padding: 1rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
                        <h2>شركة الدهان</h2>
                        <p><EMAIL> | 0569329925</p>
                    </div>

                    <div class="form-row" style="margin-bottom: 2rem;">
                        <div><strong>العميل:</strong> ${customer?.name || 'غير محدد'}</div>
                        <div><strong>تاريخ الفاتورة:</strong> ${formatDate(invoice.date)}</div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>الطول (سم)</th>
                                <th>العرض (سم)</th>
                                <th>المساحة (م²)</th>
                                <th>السعر</th>
                                <th>التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => {
                                const itemData = items.find(i => i.id === item.itemId);
                                return `
                                    <tr>
                                        <td>${itemData?.name || 'غير محدد'}</td>
                                        <td>${item.length}</td>
                                        <td>${item.width}</td>
                                        <td>${item.area}</td>
                                        <td>${formatCurrency(itemData?.pricePerSqm || 0)}</td>
                                        <td>${formatCurrency(item.cost)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>

                    <div class="invoice-summary" style="background: #f8f9ff; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>إجمالي الفاتورة:</span>
                            <span><strong>${formatCurrency(invoice.total)}</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>المدفوع:</span>
                            <span>${formatCurrency(invoice.paid)}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; border-top: 2px solid #667eea; padding-top: 0.5rem;">
                            <span><strong>المتبقي:</strong></span>
                            <span><strong>${formatCurrency(invoice.remaining)}</strong></span>
                        </div>
                    </div>

                    ${invoice.notes ? `<div style="margin-top: 1rem;"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}

                    <div class="form-group" style="margin-top: 2rem;">
                        <button class="btn btn-primary" onclick="printInvoice(${invoice.id})">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-success" onclick="addPaymentToInvoice(${invoice.id})">
                            <i class="fas fa-money-bill-wave"></i> إضافة دفعة
                        </button>
                        <button class="btn btn-secondary" onclick="closeModal('view-invoice-modal')">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        invoices = invoices.filter(i => i.id !== invoiceId);
        localStorage.setItem('invoices', JSON.stringify(invoices));
        loadInvoicesTable();
        updateDashboard();
        showNotification('تم حذف الفاتورة بنجاح', 'success');
    }
}

function addPaymentToInvoice(invoiceId) {
    const invoice = invoices.find(i => i.id === invoiceId);
    if (!invoice) return;

    const modalHtml = `
        <div class="modal" id="payment-to-invoice-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة دفعة للفاتورة #${invoice.id}</h3>
                    <button class="close-btn" onclick="closeModal('payment-to-invoice-modal')">&times;</button>
                </div>
                <form onsubmit="savePaymentToInvoice(event, ${invoiceId})">
                    <div class="form-group">
                        <label class="form-label">المبلغ المتبقي: ${formatCurrency(invoice.remaining)}</label>
                    </div>
                    <div class="form-group">
                        <label class="form-label">مبلغ الدفعة</label>
                        <input type="number" class="form-input" name="amount" max="${invoice.remaining}" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تاريخ الدفعة</label>
                        <input type="date" class="form-input" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-textarea" name="notes"></textarea>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الدفعة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('payment-to-invoice-modal')">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function savePaymentToInvoice(event, invoiceId) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const amount = parseFloat(formData.get('amount'));

    const invoice = invoices.find(i => i.id === invoiceId);
    if (!invoice) return;

    if (amount > invoice.remaining) {
        showNotification('المبلغ أكبر من المتبقي', 'error');
        return;
    }

    // Update invoice
    invoice.paid += amount;
    invoice.remaining -= amount;

    // Add payment record
    const payment = {
        id: Date.now(),
        type: 'receipt',
        customerId: invoice.customerId,
        invoiceId: invoiceId,
        amount: amount,
        currency: invoice.currency || 'SAR',
        date: formData.get('date'),
        notes: formData.get('notes')
    };

    payments.push(payment);

    localStorage.setItem('invoices', JSON.stringify(invoices));
    localStorage.setItem('payments', JSON.stringify(payments));

    loadInvoicesTable();
    loadPaymentsTable();
    closeModal('payment-to-invoice-modal');
    closeModal('view-invoice-modal');
    showNotification('تم إضافة الدفعة بنجاح', 'success');
}

// Data Export/Import Functions
function exportData() {
    const data = {
        customers,
        suppliers,
        items,
        orders,
        invoices,
        payments,
        partners,
        exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `paint-accounting-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// Currency Functions
function changeCurrency(newCurrency) {
    systemSettings.baseCurrency = newCurrency;
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    updateUserInterface();
    updateDashboard();
    loadAllTables();
    showNotification(`تم تغيير العملة إلى ${getCurrencyName(newCurrency)}`, 'success');
}

function getCurrencyName(currency) {
    const currencies = {
        'SAR': 'الريال السعودي',
        'USD': 'الدولار الأمريكي',
        'EUR': 'اليورو',
        'AED': 'الدرهم الإماراتي',
        'KWD': 'الدينار الكويتي',
        'QAR': 'الريال القطري'
    };
    return currencies[currency] || currency;
}

// Backup and Restore Functions
function openBackupManager() {
    const modalHtml = `
        <div class="modal" id="backup-manager-modal">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">إدارة النسخ الاحتياطية</h3>
                    <button class="close-btn" onclick="closeModal('backup-manager-modal')">&times;</button>
                </div>
                <div class="backup-manager-content">
                    <div class="backup-actions-grid">
                        <button class="btn btn-primary" onclick="createFullBackup()">
                            <i class="fas fa-download"></i> نسخة احتياطية كاملة
                        </button>
                        <button class="btn btn-secondary" onclick="createDataBackup()">
                            <i class="fas fa-database"></i> البيانات فقط
                        </button>
                        <button class="btn btn-warning" onclick="createSettingsBackup()">
                            <i class="fas fa-cog"></i> الإعدادات فقط
                        </button>
                        <button class="btn btn-success" onclick="restoreBackup()">
                            <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                        </button>
                    </div>

                    <div class="backup-schedule">
                        <h4>جدولة النسخ الاحتياطي</h4>
                        <div class="form-group">
                            <label class="form-label">تفعيل النسخ التلقائي</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="auto-backup-toggle" onchange="toggleAutoBackup(this.checked)">
                                <label for="auto-backup-toggle" class="toggle-label"></label>
                            </div>
                        </div>
                    </div>

                    <div class="recent-backups">
                        <h4>النسخ الاحتياطية الحديثة</h4>
                        <div id="recent-backups-list">
                            ${getRecentBackups().map(backup => `
                                <div class="backup-item">
                                    <div class="backup-info">
                                        <div class="backup-name">${backup.name}</div>
                                        <div class="backup-details">${backup.date} - ${backup.size}</div>
                                    </div>
                                    <div class="backup-actions">
                                        <button class="btn btn-sm btn-secondary" onclick="downloadBackup('${backup.id}')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').innerHTML = modalHtml;
}

function createFullBackup() {
    const backupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        type: 'full',
        data: {
            systemSettings,
            users,
            customers,
            suppliers,
            warehouses,
            rawMaterials,
            inventory,
            items,
            orders,
            invoices,
            payments,
            partners,
            expenses: expenses || []
        }
    };

    downloadBackupFile(backupData, 'full-backup');
    addBackupToHistory('full', 'نسخة احتياطية كاملة');
}

function createDataBackup() {
    const backupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        type: 'data',
        data: {
            customers,
            suppliers,
            warehouses,
            rawMaterials,
            inventory,
            items,
            orders,
            invoices,
            payments,
            partners,
            expenses: expenses || []
        }
    };

    downloadBackupFile(backupData, 'data-backup');
    addBackupToHistory('data', 'نسخة احتياطية للبيانات');
}

function createSettingsBackup() {
    const backupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        type: 'settings',
        data: {
            systemSettings,
            users
        }
    };

    downloadBackupFile(backupData, 'settings-backup');
    addBackupToHistory('settings', 'نسخة احتياطية للإعدادات');
}

function downloadBackupFile(data, type) {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${type}-${timestamp}.json`;

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = filename;
    link.click();

    showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

function restoreBackup() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                        restoreFromBackup(backupData);
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function restoreFromBackup(backupData) {
    try {
        if (backupData.type === 'full' || backupData.type === 'data') {
            if (backupData.data.customers) customers = backupData.data.customers;
            if (backupData.data.suppliers) suppliers = backupData.data.suppliers;
            if (backupData.data.warehouses) warehouses = backupData.data.warehouses;
            if (backupData.data.rawMaterials) rawMaterials = backupData.data.rawMaterials;
            if (backupData.data.inventory) inventory = backupData.data.inventory;
            if (backupData.data.items) items = backupData.data.items;
            if (backupData.data.orders) orders = backupData.data.orders;
            if (backupData.data.invoices) invoices = backupData.data.invoices;
            if (backupData.data.payments) payments = backupData.data.payments;
            if (backupData.data.partners) partners = backupData.data.partners;
            if (backupData.data.expenses) expenses = backupData.data.expenses;

            // Save to localStorage
            localStorage.setItem('customers', JSON.stringify(customers));
            localStorage.setItem('suppliers', JSON.stringify(suppliers));
            localStorage.setItem('warehouses', JSON.stringify(warehouses));
            localStorage.setItem('rawMaterials', JSON.stringify(rawMaterials));
            localStorage.setItem('inventory', JSON.stringify(inventory));
            localStorage.setItem('items', JSON.stringify(items));
            localStorage.setItem('orders', JSON.stringify(orders));
            localStorage.setItem('invoices', JSON.stringify(invoices));
            localStorage.setItem('payments', JSON.stringify(payments));
            localStorage.setItem('partners', JSON.stringify(partners));
            localStorage.setItem('expenses', JSON.stringify(expenses));
        }

        if (backupData.type === 'full' || backupData.type === 'settings') {
            if (backupData.data.systemSettings) {
                systemSettings = backupData.data.systemSettings;
                localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
            }
            if (backupData.data.users) {
                users = backupData.data.users;
                localStorage.setItem('users', JSON.stringify(users));
            }
        }

        loadAllTables();
        updateDashboard();
        updateUserInterface();
        closeModal('backup-manager-modal');
        showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');

    } catch (error) {
        showNotification('خطأ في استعادة النسخة الاحتياطية', 'error');
    }
}

function getRecentBackups() {
    const backups = JSON.parse(localStorage.getItem('backupHistory')) || [];
    return backups.slice(0, 5);
}

function addBackupToHistory(type, description) {
    let backups = JSON.parse(localStorage.getItem('backupHistory')) || [];

    const backup = {
        id: Date.now(),
        type,
        description,
        date: new Date().toLocaleString('ar-SA'),
        size: '2.5 MB', // This would be calculated in a real implementation
        user: currentUser.fullName
    };

    backups.unshift(backup);
    backups = backups.slice(0, 20); // Keep only last 20 backups

    localStorage.setItem('backupHistory', JSON.stringify(backups));
}

// Import/Export Functions
function openImportExport() {
    // Switch to import-export section
    document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
    document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));

    // Create import-export section if it doesn't exist
    let importExportSection = document.getElementById('import-export');
    if (importExportSection) {
        importExportSection.classList.add('active');
    }
}

function exportAllData() {
    const allData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        systemSettings,
        customers,
        suppliers,
        warehouses,
        rawMaterials,
        inventory,
        items,
        orders,
        invoices,
        payments,
        partners,
        expenses: expenses || []
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `complete-data-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم تصدير جميع البيانات بنجاح', 'success');
}

function exportToExcel(dataType) {
    let data = [];
    let filename = '';
    let headers = [];

    switch(dataType) {
        case 'customers':
            data = customers;
            filename = 'customers-export';
            headers = ['الرقم', 'اسم العميل', 'رقم الهاتف', 'العنوان', 'البريد الإلكتروني', 'الرصيد'];
            break;
        case 'suppliers':
            data = suppliers;
            filename = 'suppliers-export';
            headers = ['الرقم', 'اسم المورد', 'رقم الهاتف', 'العنوان', 'البريد الإلكتروني', 'الرصيد'];
            break;
        case 'items':
            data = items;
            filename = 'items-export';
            headers = ['الكود', 'اسم الصنف', 'نوع الدهان', 'رقم اللون', 'سعر المتر المربع', 'الوحدة', 'وقت التجفيف', 'التغطية', 'الوصف'];
            break;
        case 'partners':
            data = partners;
            filename = 'partners-export';
            headers = ['الرقم', 'اسم الشريك', 'نسبة الشراكة', 'رأس المال', 'الأرباح المستحقة', 'البريد الإلكتروني', 'رقم الهاتف'];
            break;
        case 'inventory':
            data = inventory;
            filename = 'inventory-export';
            headers = ['الكود', 'اسم المادة', 'الفئة', 'المخزن', 'الكمية الحالية', 'الحد الأدنى', 'الوحدة', 'آخر حركة'];
            break;
        default:
            showNotification('نوع البيانات غير مدعوم', 'error');
            return;
    }

    if (data.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'warning');
        return;
    }

    // Convert to CSV format
    const csvContent = convertToCSV(data, dataType, headers);

    // Create and download file
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();

    showNotification(`تم تصدير ${data.length} سجل بنجاح`, 'success');
}

function convertToCSV(data, dataType, headers) {
    let csv = headers.join(',') + '\n';

    data.forEach(item => {
        let row = [];

        switch(dataType) {
            case 'customers':
                row = [item.id, item.name, item.phone, item.address, item.email || '', item.balance];
                break;
            case 'suppliers':
                row = [item.id, item.name, item.phone, item.address, item.email || '', item.balance];
                break;
            case 'items':
                row = [item.code, item.name, item.paintType, item.colorCode || '', item.pricePerSqm, item.unit, item.dryingTime || '', item.coverage || '', item.description || ''];
                break;
            case 'partners':
                row = [item.id, item.name, item.percentage, item.capital, item.profits, item.email || '', item.phone || ''];
                break;
            case 'inventory':
                row = [item.code, item.name, getCategoryName(item.category), getWarehouseName(item.warehouseId), item.currentQuantity, item.minQuantity, item.unit, item.lastMovement];
                break;
        }

        // Escape commas and quotes in data
        const escapedRow = row.map(field => {
            if (typeof field === 'string' && (field.includes(',') || field.includes('"'))) {
                return `"${field.replace(/"/g, '""')}"`;
            }
            return field;
        });

        csv += escapedRow.join(',') + '\n';
    });

    return csv;
}

function downloadTemplate(dataType) {
    let headers = [];
    let sampleData = [];
    let filename = '';

    switch(dataType) {
        case 'customers':
            headers = ['الرقم', 'اسم العميل', 'رقم الهاتف', 'العنوان', 'البريد الإلكتروني', 'الرصيد'];
            sampleData = [['1', 'أحمد محمد', '0501234567', 'الرياض', '<EMAIL>', '0']];
            filename = 'template-customers';
            break;
        case 'suppliers':
            headers = ['الرقم', 'اسم المورد', 'رقم الهاتف', 'العنوان', 'البريد الإلكتروني', 'الرصيد'];
            sampleData = [['1', 'شركة الدهانات', '0112345678', 'الرياض', '<EMAIL>', '0']];
            filename = 'template-suppliers';
            break;
        case 'items':
            headers = ['الكود', 'اسم الصنف', 'نوع الدهان', 'رقم اللون', 'سعر المتر المربع', 'الوحدة', 'وقت التجفيف', 'التغطية', 'الوصف'];
            sampleData = [['PROD-001', 'دهان أبيض', 'دهان عادي', 'RAL-9010', '25', 'م²', '4 ساعات', '12 م²/لتر', 'دهان عالي الجودة']];
            filename = 'template-items';
            break;
        case 'partners':
            headers = ['الرقم', 'اسم الشريك', 'نسبة الشراكة', 'رأس المال', 'البريد الإلكتروني', 'رقم الهاتف'];
            sampleData = [['1', 'فارس نواف', '60', '100000', '<EMAIL>', '0569329925']];
            filename = 'template-partners';
            break;
    }

    let csv = headers.join(',') + '\n';
    sampleData.forEach(row => {
        csv += row.join(',') + '\n';
    });

    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${filename}.csv`;
    link.click();

    showNotification('تم تحميل القالب بنجاح', 'success');
}

let importPreviewData = null;

function handleExcelImport(event) {
    const file = event.target.files[0];
    const importType = document.getElementById('import-type').value;

    if (!importType) {
        showNotification('يرجى اختيار نوع البيانات أولاً', 'error');
        return;
    }

    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const text = e.target.result;
            const lines = text.split('\n').filter(line => line.trim());

            if (lines.length < 2) {
                showNotification('الملف فارغ أو لا يحتوي على بيانات صالحة', 'error');
                return;
            }

            // Parse CSV data
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            const data = [];

            for (let i = 1; i < lines.length; i++) {
                const values = parseCSVLine(lines[i]);
                if (values.length === headers.length) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index];
                    });
                    data.push(row);
                }
            }

            if (data.length === 0) {
                showNotification('لم يتم العثور على بيانات صالحة في الملف', 'error');
                return;
            }

            importPreviewData = { type: importType, data: data };
            showImportPreview(data, importType);

        } catch (error) {
            showNotification('خطأ في قراءة الملف', 'error');
        }
    };

    reader.readAsText(file, 'UTF-8');
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result;
}

function showImportPreview(data, importType) {
    const previewContainer = document.getElementById('import-preview');
    const previewTable = document.getElementById('preview-table');

    if (!previewContainer || !previewTable) return;

    // Show only first 5 rows for preview
    const previewData = data.slice(0, 5);
    const headers = Object.keys(previewData[0]);

    let tableHtml = '<table class="preview-table"><thead><tr>';
    headers.forEach(header => {
        tableHtml += `<th>${header}</th>`;
    });
    tableHtml += '</tr></thead><tbody>';

    previewData.forEach(row => {
        tableHtml += '<tr>';
        headers.forEach(header => {
            tableHtml += `<td>${row[header] || ''}</td>`;
        });
        tableHtml += '</tr>';
    });

    tableHtml += '</tbody></table>';
    tableHtml += `<p style="margin-top: 1rem; color: #718096;">معاينة ${previewData.length} من أصل ${data.length} سجل</p>`;

    previewTable.innerHTML = tableHtml;
    previewContainer.style.display = 'block';
}

function confirmImport() {
    if (!importPreviewData) {
        showNotification('لا توجد بيانات للاستيراد', 'error');
        return;
    }

    try {
        const { type, data } = importPreviewData;
        let importedCount = 0;

        switch(type) {
            case 'customers':
                importedCount = importCustomers(data);
                break;
            case 'suppliers':
                importedCount = importSuppliers(data);
                break;
            case 'items':
                importedCount = importItems(data);
                break;
            case 'partners':
                importedCount = importPartners(data);
                break;
            case 'inventory':
                importedCount = importInventory(data);
                break;
        }

        if (importedCount > 0) {
            loadAllTables();
            updateDashboard();
            cancelImport();
            showNotification(`تم استيراد ${importedCount} سجل بنجاح`, 'success');

            // Add to import history
            addImportToHistory(type, importedCount);
        } else {
            showNotification('لم يتم استيراد أي سجل', 'warning');
        }

    } catch (error) {
        showNotification('خطأ في عملية الاستيراد', 'error');
    }
}

function cancelImport() {
    importPreviewData = null;
    document.getElementById('import-preview').style.display = 'none';
    document.getElementById('excel-file-input').value = '';
    document.getElementById('import-type').value = '';
}

function importCustomers(data) {
    let importedCount = 0;

    data.forEach(row => {
        try {
            const customer = {
                id: parseInt(row['الرقم']) || Date.now() + Math.random(),
                name: row['اسم العميل'] || '',
                phone: row['رقم الهاتف'] || '',
                address: row['العنوان'] || '',
                email: row['البريد الإلكتروني'] || '',
                balance: parseFloat(row['الرصيد']) || 0
            };

            if (customer.name && customer.phone) {
                // Check if customer already exists
                const existingIndex = customers.findIndex(c => c.id === customer.id || c.phone === customer.phone);
                if (existingIndex >= 0) {
                    customers[existingIndex] = customer;
                } else {
                    customers.push(customer);
                }
                importedCount++;
            }
        } catch (error) {
            console.error('Error importing customer:', error);
        }
    });

    localStorage.setItem('customers', JSON.stringify(customers));
    return importedCount;
}

function importSuppliers(data) {
    let importedCount = 0;

    data.forEach(row => {
        try {
            const supplier = {
                id: parseInt(row['الرقم']) || Date.now() + Math.random(),
                name: row['اسم المورد'] || '',
                phone: row['رقم الهاتف'] || '',
                address: row['العنوان'] || '',
                email: row['البريد الإلكتروني'] || '',
                balance: parseFloat(row['الرصيد']) || 0
            };

            if (supplier.name && supplier.phone) {
                const existingIndex = suppliers.findIndex(s => s.id === supplier.id || s.phone === supplier.phone);
                if (existingIndex >= 0) {
                    suppliers[existingIndex] = supplier;
                } else {
                    suppliers.push(supplier);
                }
                importedCount++;
            }
        } catch (error) {
            console.error('Error importing supplier:', error);
        }
    });

    localStorage.setItem('suppliers', JSON.stringify(suppliers));
    return importedCount;
}

function importItems(data) {
    let importedCount = 0;

    data.forEach(row => {
        try {
            const item = {
                id: Date.now() + Math.random(),
                code: row['الكود'] || '',
                name: row['اسم الصنف'] || '',
                paintType: row['نوع الدهان'] || '',
                colorCode: row['رقم اللون'] || '',
                pricePerSqm: parseFloat(row['سعر المتر المربع']) || 0,
                unit: row['الوحدة'] || 'م²',
                dryingTime: row['وقت التجفيف'] || '',
                coverage: row['التغطية'] || '',
                description: row['الوصف'] || ''
            };

            if (item.name && item.pricePerSqm > 0) {
                const existingIndex = items.findIndex(i => i.code === item.code || i.name === item.name);
                if (existingIndex >= 0) {
                    items[existingIndex] = item;
                } else {
                    items.push(item);
                }
                importedCount++;
            }
        } catch (error) {
            console.error('Error importing item:', error);
        }
    });

    localStorage.setItem('items', JSON.stringify(items));
    return importedCount;
}

function importPartners(data) {
    let importedCount = 0;

    data.forEach(row => {
        try {
            const partner = {
                id: parseInt(row['الرقم']) || Date.now() + Math.random(),
                name: row['اسم الشريك'] || '',
                percentage: parseFloat(row['نسبة الشراكة']) || 0,
                capital: parseFloat(row['رأس المال']) || 0,
                profits: 0,
                email: row['البريد الإلكتروني'] || '',
                phone: row['رقم الهاتف'] || ''
            };

            if (partner.name && partner.percentage > 0) {
                const existingIndex = partners.findIndex(p => p.id === partner.id || p.name === partner.name);
                if (existingIndex >= 0) {
                    partners[existingIndex] = { ...partners[existingIndex], ...partner };
                } else {
                    partners.push(partner);
                }
                importedCount++;
            }
        } catch (error) {
            console.error('Error importing partner:', error);
        }
    });

    localStorage.setItem('partners', JSON.stringify(partners));
    return importedCount;
}

function importInventory(data) {
    let importedCount = 0;

    data.forEach(row => {
        try {
            const inventoryItem = {
                id: Date.now() + Math.random(),
                code: row['الكود'] || '',
                name: row['اسم المادة'] || '',
                category: getCategoryCode(row['الفئة']) || 'paint',
                warehouseId: getWarehouseId(row['المخزن']) || 1,
                currentQuantity: parseFloat(row['الكمية الحالية']) || 0,
                minQuantity: parseFloat(row['الحد الأدنى']) || 0,
                unit: row['الوحدة'] || 'لتر',
                lastMovement: row['آخر حركة'] || new Date().toISOString().split('T')[0]
            };

            if (inventoryItem.name && inventoryItem.code) {
                const existingIndex = inventory.findIndex(i => i.code === inventoryItem.code);
                if (existingIndex >= 0) {
                    inventory[existingIndex] = inventoryItem;
                } else {
                    inventory.push(inventoryItem);
                }
                importedCount++;
            }
        } catch (error) {
            console.error('Error importing inventory item:', error);
        }
    });

    localStorage.setItem('inventory', JSON.stringify(inventory));
    return importedCount;
}

function getCategoryCode(categoryName) {
    const categoryMap = {
        'دهانات': 'paint',
        'مذيبات': 'thinner',
        'برايمر': 'primer',
        'ورنيش': 'varnish',
        'أدوات': 'tools',
        'إكسسوارات': 'accessories'
    };
    return categoryMap[categoryName] || 'paint';
}

function getWarehouseId(warehouseName) {
    const warehouse = warehouses.find(w => w.name === warehouseName);
    return warehouse ? warehouse.id : 1;
}

function addImportToHistory(type, recordCount) {
    let importHistory = JSON.parse(localStorage.getItem('importHistory')) || [];

    const importRecord = {
        id: Date.now(),
        date: new Date().toLocaleString('ar-SA'),
        type: type,
        recordCount: recordCount,
        user: currentUser.fullName,
        status: 'success'
    };

    importHistory.unshift(importRecord);
    importHistory = importHistory.slice(0, 50); // Keep only last 50 imports

    localStorage.setItem('importHistory', JSON.stringify(importHistory));
}

function handleBackupImport(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const backupData = JSON.parse(e.target.result);

                if (confirm('هل أنت متأكد من استيراد النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                    restoreFromBackup(backupData);
                    showNotification('تم استيراد النسخة الاحتياطية بنجاح', 'success');
                }
            } catch (error) {
                showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
            }
        };
        reader.readAsText(file);
    }
}
