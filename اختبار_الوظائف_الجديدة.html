<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الوظائف الجديدة - نظام الدهان</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-section h2 i {
            color: #4CAF50;
        }

        .test-steps {
            list-style: none;
            counter-reset: step-counter;
        }

        .test-steps li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            position: relative;
            padding-left: 60px;
        }

        .test-steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #4CAF50;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .test-button.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .test-button.info {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .test-result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .workflow-indicator {
            background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }

        .feature-highlight {
            background: linear-gradient(135deg, #FFE066 0%, #FF6B6B 100%);
            color: #2d3748;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #ffd700;
        }

        .feature-highlight h3 {
            margin-bottom: 10px;
            color: #d63031;
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
            
            .test-section {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار الوظائف الجديدة</h1>
            <p>تأكد من عمل جميع الوظائف المضافة للدورة المستندية</p>
        </div>

        <div class="content">
            <!-- اختبار تغيير حالة أمر الإنتاج -->
            <div class="test-section">
                <div class="workflow-indicator">🔨 مرحلة التنفيذ</div>
                <h2><i class="fas fa-tasks"></i> اختبار تغيير حالة أمر الإنتاج</h2>
                
                <div class="feature-highlight">
                    <h3>🆕 الميزة الجديدة:</h3>
                    <p>تم إضافة إمكانية تغيير حالة أمر الإنتاج مع خصم المواد الخام تلقائياً عند الإكمال</p>
                </div>

                <ol class="test-steps">
                    <li>افتح النظام واذهب لقسم 'أوامر الإنتاج'</li>
                    <li>أنشئ أمر إنتاج جديد أو استخدم أمر موجود</li>
                    <li>اضغط على زر 'تحديث الحالة' (أيقونة المهام الصفراء)</li>
                    <li>غيّر الحالة من 'في الانتظار' إلى 'قيد التنفيذ'</li>
                    <li>أضف ملاحظات ونسبة الإنجاز (مثل 50%)</li>
                    <li>احفظ التحديث وتأكد من تغيير الحالة في الجدول</li>
                </ol>

                <button class="test-button info" onclick="testOrderStatus()">
                    <i class="fas fa-play"></i> اختبار تحديث الحالة
                </button>
                
                <div id="order-status-result" class="test-result"></div>
            </div>

            <!-- اختبار خصم المواد الخام -->
            <div class="test-section">
                <div class="workflow-indicator">📦 خصم المخزون</div>
                <h2><i class="fas fa-boxes"></i> اختبار خصم المواد الخام التلقائي</h2>
                
                <div class="feature-highlight">
                    <h3>🆕 الميزة الجديدة:</h3>
                    <p>النظام يخصم المواد الخام تلقائياً عند تمييز أمر الإنتاج كمكتمل</p>
                </div>

                <ol class="test-steps">
                    <li>تأكد من وجود مواد خام في المخزون (اذهب لقسم 'المواد الخام')</li>
                    <li>أضف مادة خام جديدة إذا لم توجد (مثل: دهان أكريليك أبيض)</li>
                    <li>ارجع لقسم 'أوامر الإنتاج'</li>
                    <li>اختر أمر إنتاج قيد التنفيذ</li>
                    <li>اضغط زر 'تمييز كمكتمل' (أيقونة الصح الخضراء)</li>
                    <li>أكد العملية وراقب رسائل النظام</li>
                    <li>تحقق من خصم المواد في قسم 'المواد الخام'</li>
                </ol>

                <button class="test-button success" onclick="testMaterialDeduction()">
                    <i class="fas fa-check-circle"></i> اختبار خصم المواد
                </button>
                
                <div id="material-deduction-result" class="test-result"></div>
            </div>

            <!-- اختبار إنشاء فاتورة من أمر الإنتاج -->
            <div class="test-section">
                <div class="workflow-indicator">🧾 الفوترة</div>
                <h2><i class="fas fa-file-invoice"></i> اختبار إنشاء فاتورة من أمر الإنتاج</h2>
                
                <div class="feature-highlight">
                    <h3>🆕 الميزة الجديدة:</h3>
                    <p>إنشاء فاتورة تلقائياً من أمر الإنتاج المكتمل مع نقل جميع التفاصيل</p>
                </div>

                <ol class="test-steps">
                    <li>تأكد من وجود أمر إنتاج مكتمل</li>
                    <li>اضغط زر 'إنشاء فاتورة' (أيقونة الفاتورة الخضراء)</li>
                    <li>النظام سيسأل عن إنشاء فاتورة - اختر 'موافق'</li>
                    <li>انتظر انتقال النظام لقسم 'الفواتير'</li>
                    <li>تحقق من إنشاء الفاتورة الجديدة</li>
                    <li>راجع تفاصيل الفاتورة (العميل، الأصناف، الأسعار)</li>
                    <li>تأكد من ربط الفاتورة بأمر الإنتاج الأصلي</li>
                </ol>

                <button class="test-button warning" onclick="testInvoiceCreation()">
                    <i class="fas fa-file-invoice"></i> اختبار إنشاء الفاتورة
                </button>
                
                <div id="invoice-creation-result" class="test-result"></div>
            </div>

            <!-- اختبار الدورة المستندية الكاملة -->
            <div class="test-section">
                <div class="workflow-indicator">🔄 الدورة الكاملة</div>
                <h2><i class="fas fa-sync-alt"></i> اختبار الدورة المستندية الكاملة</h2>
                
                <div class="feature-highlight">
                    <h3>🎯 الهدف:</h3>
                    <p>اختبار التدفق الكامل من إنشاء أمر الإنتاج حتى إصدار الفاتورة</p>
                </div>

                <ol class="test-steps">
                    <li>أنشئ أمر إنتاج جديد مع تفاصيل كاملة</li>
                    <li>غيّر الحالة إلى 'قيد التنفيذ'</li>
                    <li>غيّر الحالة إلى 'مكتمل' (سيخصم المواد تلقائياً)</li>
                    <li>أنشئ فاتورة من الأمر المكتمل</li>
                    <li>راجع الفاتورة وتأكد من صحة البيانات</li>
                    <li>اطبع الفاتورة للعميل</li>
                </ol>

                <button class="test-button" onclick="testCompleteWorkflow()">
                    <i class="fas fa-play-circle"></i> اختبار الدورة الكاملة
                </button>
                
                <div id="complete-workflow-result" class="test-result"></div>
            </div>
        </div>
    </div>

    <script>
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i> ${message}`;
            element.style.display = 'block';
        }

        function testOrderStatus() {
            showResult('order-status-result', 'info', 'افتح النظام الرئيسي واتبع الخطوات المذكورة أعلاه. تأكد من ظهور نافذة تحديث الحالة عند الضغط على زر "تحديث الحالة".');
        }

        function testMaterialDeduction() {
            showResult('material-deduction-result', 'info', 'تأكد من وجود مواد خام في النظام أولاً، ثم اتبع الخطوات. يجب أن ترى رسالة تأكيد خصم المواد عند تمييز الأمر كمكتمل.');
        }

        function testInvoiceCreation() {
            showResult('invoice-creation-result', 'info', 'تأكد من وجود أمر إنتاج مكتمل، ثم اضغط زر "إنشاء فاتورة". يجب أن ينتقل النظام تلقائياً لقسم الفواتير مع الفاتورة الجديدة.');
        }

        function testCompleteWorkflow() {
            showResult('complete-workflow-result', 'success', 'ابدأ بإنشاء أمر إنتاج جديد واتبع جميع الخطوات بالترتيب. هذا سيختبر التدفق الكامل للدورة المستندية الجديدة.');
        }

        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.test-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
