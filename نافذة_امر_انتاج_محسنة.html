<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة أمر الإنتاج المحسنة - تصميم احترافي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', 'Cairo', sans-serif;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 3px solid #e9ecef;
        }
        
        .demo-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-header p {
            color: #718096;
            font-size: 1.2rem;
            line-height: 1.6;
        }
        
        .features-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #4299e1;
            transition: transform 0.3s ease;
        }
        
        .feature-highlight:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(66, 153, 225, 0.2);
        }
        
        .feature-highlight h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .feature-highlight ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-highlight li {
            padding: 8px 0;
            color: #4a5568;
            position: relative;
            padding-right: 25px;
        }
        
        .feature-highlight li:before {
            content: "✓";
            position: absolute;
            right: 0;
            color: #48bb78;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .demo-button {
            display: block;
            width: 400px;
            margin: 40px auto;
            padding: 20px 40px;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.4rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s ease;
            text-decoration: none;
            text-align: center;
            box-shadow: 0 10px 30px rgba(66, 153, 225, 0.3);
        }
        
        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(66, 153, 225, 0.4);
            background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
        }
        
        .demo-button i {
            margin-left: 15px;
            font-size: 1.2rem;
        }
        
        .modal {
            display: block !important;
        }
        
        .instructions {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            color: #4a5568;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-magic"></i> نافذة أمر الإنتاج المحسنة</h1>
            <p>تصميم احترافي مضغوط مع تخطيط أفقي وحسابات تلقائية دقيقة</p>
        </div>
        
        <div class="features-showcase">
            <div class="feature-highlight">
                <h3><i class="fas fa-layout-alt"></i> التصميم المحسن</h3>
                <ul>
                    <li>تخطيط أفقي مضغوط</li>
                    <li>سطرين فوق منظمين</li>
                    <li>واجهة احترافية</li>
                    <li>تنسيق مضغوط وأنيق</li>
                </ul>
            </div>
            
            <div class="feature-highlight">
                <h3><i class="fas fa-calculator"></i> الحسابات التلقائية</h3>
                <ul>
                    <li>حساب المساحة فوري</li>
                    <li>تحديث الأسعار تلقائياً</li>
                    <li>حساب الإجمالي الذكي</li>
                    <li>ملخص تكلفة شامل</li>
                </ul>
            </div>
            
            <div class="feature-highlight">
                <h3><i class="fas fa-shield-alt"></i> فحص الأخطاء</h3>
                <ul>
                    <li>التحقق من البيانات</li>
                    <li>رسائل خطأ واضحة</li>
                    <li>تمييز الحقول الخاطئة</li>
                    <li>منع الحفظ مع أخطاء</li>
                </ul>
            </div>
            
            <div class="feature-highlight">
                <h3><i class="fas fa-mobile-alt"></i> التصميم المتجاوب</h3>
                <ul>
                    <li>يعمل على جميع الشاشات</li>
                    <li>تخطيط متكيف</li>
                    <li>سهولة الاستخدام</li>
                    <li>تجربة مستخدم ممتازة</li>
                </ul>
            </div>
        </div>
        
        <button class="demo-button" onclick="openOrderModal()">
            <i class="fas fa-rocket"></i>
            تجربة النافذة المحسنة الآن
        </button>
        
        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> تعليمات الاستخدام:</h3>
            <ol>
                <li>انقر على الزر أعلاه لفتح النافذة المحسنة</li>
                <li>لاحظ التصميم الأفقي المضغوط مع السطرين المنظمين</li>
                <li>جرب إدخال بيانات الأصناف ولاحظ الحسابات التلقائية</li>
                <li>اختبر فحص الأخطاء بإدخال قيم خاطئة</li>
                <li>راجع ملخص التكلفة الشامل في الأسفل</li>
            </ol>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script src="script.js"></script>
    
    <script>
        // Initialize sample data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Make sure sample data is loaded
            if (typeof addSampleData === 'function') {
                addSampleData();
            }
            
            console.log('النافذة المحسنة جاهزة للاختبار!');
        });
        
        // Override closeModal to work with our demo
        function closeModal(modalId) {
            document.getElementById('modal-container').innerHTML = '';
        }
        
        // Add some demo functionality
        window.addEventListener('load', function() {
            console.log('تم تحميل النافذة المحسنة بنجاح!');
        });
    </script>
</body>
</html>
