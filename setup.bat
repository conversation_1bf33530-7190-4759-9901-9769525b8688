@echo off
chcp 65001 >nul
echo ========================================
echo    نظام محاسبة شركة الدهان
echo    إعداد وتثبيت البرنامج
echo ========================================
echo.
echo المطور: فارس نواف
echo البريد الإلكتروني: <EMAIL>
echo الهاتف: 0569329925
echo.
echo ========================================
echo.

echo جاري التحقق من الملفات المطلوبة...
echo.

if not exist "index.html" (
    echo خطأ: ملف index.html غير موجود
    pause
    exit /b 1
)

if not exist "styles.css" (
    echo خطأ: ملف styles.css غير موجود
    pause
    exit /b 1
)

if not exist "script.js" (
    echo خطأ: ملف script.js غير موجود
    pause
    exit /b 1
)

echo ✓ جميع الملفات المطلوبة موجودة
echo.

echo جاري إنشاء اختصار على سطح المكتب...

set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\نظام محاسبة شركة الدهان.url"
set "currentdir=%cd%"

echo [InternetShortcut] > "%shortcut%"
echo URL=file:///%currentdir:\=/%/index.html >> "%shortcut%"
echo IconFile=%SystemRoot%\System32\shell32.dll >> "%shortcut%"
echo IconIndex=165 >> "%shortcut%"

if exist "%shortcut%" (
    echo ✓ تم إنشاء اختصار على سطح المكتب بنجاح
) else (
    echo ! فشل في إنشاء الاختصار
)

echo.
echo ========================================
echo تم تثبيت البرنامج بنجاح!
echo ========================================
echo.
echo لتشغيل البرنامج:
echo 1. انقر نقراً مزدوجاً على الاختصار الموجود على سطح المكتب
echo 2. أو افتح ملف index.html مباشرة
echo.
echo ملاحظات مهمة:
echo - البرنامج يعمل في المتصفح ولا يحتاج اتصال بالإنترنت
echo - البيانات محفوظة محلياً في المتصفح
echo - استخدم خاصية التصدير لعمل نسخة احتياطية
echo.
echo للدعم التقني:
echo البريد الإلكتروني: <EMAIL>
echo الهاتف: 0569329925
echo.

choice /c YN /m "هل تريد تشغيل البرنامج الآن؟ (Y/N)"
if errorlevel 2 goto end
if errorlevel 1 goto run

:run
echo.
echo جاري تشغيل البرنامج...
start "" "%currentdir%\index.html"
goto end

:end
echo.
echo شكراً لاستخدام نظام محاسبة شركة الدهان
echo.
pause
