<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تغيير حالة الطلب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
        }
        .form-select {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background: #0078d4; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            z-index: 2000;
        }
    </style>
</head>
<body>
    <h1>اختبار تغيير حالة الطلب</h1>
    
    <button onclick="openTestModal()">فتح نافذة اختبار</button>
    
    <div id="modal-container"></div>
    <div id="notification-container"></div>

    <script>
        // بيانات تجريبية
        let orders = [
            {
                id: 1,
                orderNumber: 'ORD-001',
                customerId: 1,
                status: 'pending',
                orderDate: new Date().toISOString(),
                deliveryDate: new Date().toISOString(),
                materialCost: 500,
                laborCost: 300,
                totalCost: 800,
                items: [
                    {
                        itemId: 1,
                        length: 4,
                        width: 3,
                        quantity: 2,
                        area: 24,
                        pricePerSqm: 25,
                        totalPrice: 600
                    }
                ]
            }
        ];

        let customers = [
            { id: 1, name: 'أحمد محمد' }
        ];

        function getStatusText(status) {
            const statusMap = {
                'pending': 'في الانتظار',
                'processing': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };
            return statusMap[status] || status;
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.getElementById('notification-container').appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function openTestModal() {
            const order = orders[0];
            const customer = customers[0];

            const modalHtml = `
                <div class="modal" id="test-modal">
                    <div class="modal-content">
                        <h3>تفاصيل أمر الإنتاج ${order.orderNumber}</h3>
                        
                        <p><strong>العميل:</strong> ${customer.name}</p>
                        <p><strong>الحالة الحالية:</strong> ${getStatusText(order.status)}</p>
                        
                        <!-- تغيير الحالة -->
                        <div style="background: #f0f8ff; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <h4>تغيير حالة الطلب</h4>
                            <div style="display: flex; gap: 1rem; align-items: center;">
                                <label>الحالة الجديدة:</label>
                                <select id="order-status-${order.id}" class="form-select" onchange="updateOrderStatus(${order.id}, this.value)">
                                    <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>في الانتظار</option>
                                    <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>قيد التنفيذ</option>
                                    <option value="completed" ${order.status === 'completed' ? 'selected' : ''}>مكتمل</option>
                                    <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>ملغي</option>
                                </select>
                            </div>
                        </div>

                        <div style="margin-top: 1.5rem;" id="button-container">
                            ${order.status === 'completed' ?
                                '<button class="btn btn-primary">إنشاء فاتورة</button>' : ''
                            }
                            <button class="btn btn-info">طباعة الأمر</button>
                            <button class="btn btn-success">تعديل الأمر</button>
                            <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modal-container').innerHTML = modalHtml;
        }

        function updateOrderStatus(orderId, newStatus) {
            console.log('تحديث حالة الطلب:', orderId, newStatus);
            
            const order = orders.find(o => o.id === orderId);
            if (!order) {
                console.error('الطلب غير موجود:', orderId);
                return;
            }

            const oldStatus = order.status;
            order.status = newStatus;
            
            showNotification(`تم تغيير حالة الطلب من "${getStatusText(oldStatus)}" إلى "${getStatusText(newStatus)}"`, 'success');
            
            // تحديث الأزرار
            const buttonContainer = document.getElementById('button-container');
            if (buttonContainer) {
                const createInvoiceBtn = newStatus === 'completed' ?
                    '<button class="btn btn-primary">إنشاء فاتورة</button>' : '';
                
                buttonContainer.innerHTML = `
                    ${createInvoiceBtn}
                    <button class="btn btn-info">طباعة الأمر</button>
                    <button class="btn btn-success">تعديل الأمر</button>
                    <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                `;
            }
            
            // إذا تم تمييز الطلب كمكتمل
            if (newStatus === 'completed') {
                setTimeout(() => {
                    if (confirm('تم تمييز الطلب كمكتمل. هل تريد إنشاء فاتورة الآن؟')) {
                        alert('سيتم إنشاء الفاتورة...');
                    }
                }, 500);
            }
        }

        function closeModal() {
            document.getElementById('modal-container').innerHTML = '';
        }
    </script>
</body>
</html>
